export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      ai_insights: {
        Row: {
          category: string
          confidence_score: number | null
          created_date: string | null
          date_key: number | null
          description: string
          expires_at: string | null
          impact_score: number | null
          insight_data: Json | null
          insight_id: string
          insight_key: number
          insight_type: string
          location_key: number | null
          recommended_actions: Json | null
          resolved_at: string | null
          resolved_by: string | null
          severity: string | null
          status: string | null
          tenant_key: number | null
          time_key: number | null
          title: string
          updated_date: string | null
        }
        Insert: {
          category: string
          confidence_score?: number | null
          created_date?: string | null
          date_key?: number | null
          description: string
          expires_at?: string | null
          impact_score?: number | null
          insight_data?: Json | null
          insight_id?: string
          insight_key?: number
          insight_type: string
          location_key?: number | null
          recommended_actions?: Json | null
          resolved_at?: string | null
          resolved_by?: string | null
          severity?: string | null
          status?: string | null
          tenant_key?: number | null
          time_key?: number | null
          title: string
          updated_date?: string | null
        }
        Update: {
          category?: string
          confidence_score?: number | null
          created_date?: string | null
          date_key?: number | null
          description?: string
          expires_at?: string | null
          impact_score?: number | null
          insight_data?: Json | null
          insight_id?: string
          insight_key?: number
          insight_type?: string
          location_key?: number | null
          recommended_actions?: Json | null
          resolved_at?: string | null
          resolved_by?: string | null
          severity?: string | null
          status?: string | null
          tenant_key?: number | null
          time_key?: number | null
          title?: string
          updated_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "ai_insights_location_key_fkey"
            columns: ["location_key"]
            isOneToOne: false
            referencedRelation: "dim_location"
            referencedColumns: ["location_key"]
          },
          {
            foreignKeyName: "ai_insights_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
        ]
      }
      ai_model_performance: {
        Row: {
          accuracy_score: number | null
          created_date: string | null
          deployment_date: string | null
          evaluation_date: string | null
          f1_score: number | null
          feature_count: number | null
          hyperparameters: Json | null
          is_production_ready: boolean | null
          mae_score: number | null
          model_id: string
          model_name: string
          model_type: string
          model_version: string
          performance_key: number
          performance_metrics: Json | null
          precision_score: number | null
          recall_score: number | null
          rmse_score: number | null
          tenant_key: number | null
          training_date: string | null
          training_samples: number | null
          updated_date: string | null
          validation_samples: number | null
        }
        Insert: {
          accuracy_score?: number | null
          created_date?: string | null
          deployment_date?: string | null
          evaluation_date?: string | null
          f1_score?: number | null
          feature_count?: number | null
          hyperparameters?: Json | null
          is_production_ready?: boolean | null
          mae_score?: number | null
          model_id?: string
          model_name: string
          model_type: string
          model_version: string
          performance_key?: number
          performance_metrics?: Json | null
          precision_score?: number | null
          recall_score?: number | null
          rmse_score?: number | null
          tenant_key?: number | null
          training_date?: string | null
          training_samples?: number | null
          updated_date?: string | null
          validation_samples?: number | null
        }
        Update: {
          accuracy_score?: number | null
          created_date?: string | null
          deployment_date?: string | null
          evaluation_date?: string | null
          f1_score?: number | null
          feature_count?: number | null
          hyperparameters?: Json | null
          is_production_ready?: boolean | null
          mae_score?: number | null
          model_id?: string
          model_name?: string
          model_type?: string
          model_version?: string
          performance_key?: number
          performance_metrics?: Json | null
          precision_score?: number | null
          recall_score?: number | null
          rmse_score?: number | null
          tenant_key?: number | null
          training_date?: string | null
          training_samples?: number | null
          updated_date?: string | null
          validation_samples?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "ai_model_performance_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
        ]
      }
      anomaly_detection: {
        Row: {
          actual_value: number | null
          anomaly_id: string
          anomaly_key: number
          anomaly_score: number | null
          business_impact: string | null
          confirmed_at: string | null
          confirmed_by: string | null
          created_date: string | null
          date_key: number | null
          detection_method: string | null
          deviation_percentage: number | null
          expected_value: number | null
          is_confirmed: boolean | null
          location_key: number | null
          metric_name: string
          root_cause_analysis: Json | null
          tenant_key: number | null
          threshold_breached: string | null
          time_key: number | null
          updated_date: string | null
        }
        Insert: {
          actual_value?: number | null
          anomaly_id?: string
          anomaly_key?: number
          anomaly_score?: number | null
          business_impact?: string | null
          confirmed_at?: string | null
          confirmed_by?: string | null
          created_date?: string | null
          date_key?: number | null
          detection_method?: string | null
          deviation_percentage?: number | null
          expected_value?: number | null
          is_confirmed?: boolean | null
          location_key?: number | null
          metric_name: string
          root_cause_analysis?: Json | null
          tenant_key?: number | null
          threshold_breached?: string | null
          time_key?: number | null
          updated_date?: string | null
        }
        Update: {
          actual_value?: number | null
          anomaly_id?: string
          anomaly_key?: number
          anomaly_score?: number | null
          business_impact?: string | null
          confirmed_at?: string | null
          confirmed_by?: string | null
          created_date?: string | null
          date_key?: number | null
          detection_method?: string | null
          deviation_percentage?: number | null
          expected_value?: number | null
          is_confirmed?: boolean | null
          location_key?: number | null
          metric_name?: string
          root_cause_analysis?: Json | null
          tenant_key?: number | null
          threshold_breached?: string | null
          time_key?: number | null
          updated_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "anomaly_detection_location_key_fkey"
            columns: ["location_key"]
            isOneToOne: false
            referencedRelation: "dim_location"
            referencedColumns: ["location_key"]
          },
          {
            foreignKeyName: "anomaly_detection_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
        ]
      }
      bi_metrics: {
        Row: {
          benchmark_value: number | null
          calculation_method: string | null
          created_date: string | null
          data_sources: Json | null
          date_key: number | null
          industry_benchmark: number | null
          location_key: number | null
          metric_category: string
          metric_id: string
          metric_key: number
          metric_name: string
          metric_unit: string | null
          metric_value: number | null
          seasonality_factor: number | null
          tenant_key: number | null
          trend_direction: string | null
          trend_strength: number | null
          updated_date: string | null
          variance_from_benchmark: number | null
        }
        Insert: {
          benchmark_value?: number | null
          calculation_method?: string | null
          created_date?: string | null
          data_sources?: Json | null
          date_key?: number | null
          industry_benchmark?: number | null
          location_key?: number | null
          metric_category: string
          metric_id?: string
          metric_key?: number
          metric_name: string
          metric_unit?: string | null
          metric_value?: number | null
          seasonality_factor?: number | null
          tenant_key?: number | null
          trend_direction?: string | null
          trend_strength?: number | null
          updated_date?: string | null
          variance_from_benchmark?: number | null
        }
        Update: {
          benchmark_value?: number | null
          calculation_method?: string | null
          created_date?: string | null
          data_sources?: Json | null
          date_key?: number | null
          industry_benchmark?: number | null
          location_key?: number | null
          metric_category?: string
          metric_id?: string
          metric_key?: number
          metric_name?: string
          metric_unit?: string | null
          metric_value?: number | null
          seasonality_factor?: number | null
          tenant_key?: number | null
          trend_direction?: string | null
          trend_strength?: number | null
          updated_date?: string | null
          variance_from_benchmark?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "bi_metrics_location_key_fkey"
            columns: ["location_key"]
            isOneToOne: false
            referencedRelation: "dim_location"
            referencedColumns: ["location_key"]
          },
          {
            foreignKeyName: "bi_metrics_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
        ]
      }
      companies: {
        Row: {
          created_at: string
          created_by: string | null
          id: string
          industry: string | null
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          id?: string
          industry?: string | null
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string | null
          id?: string
          industry?: string | null
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      data_ingestion_logs: {
        Row: {
          completed_at: string | null
          created_by: string | null
          error_details: Json | null
          id: string
          location_id: string | null
          records_failed: number | null
          records_processed: number | null
          source_name: string
          source_type: string
          started_at: string
          status: string
        }
        Insert: {
          completed_at?: string | null
          created_by?: string | null
          error_details?: Json | null
          id?: string
          location_id?: string | null
          records_failed?: number | null
          records_processed?: number | null
          source_name: string
          source_type: string
          started_at?: string
          status?: string
        }
        Update: {
          completed_at?: string | null
          created_by?: string | null
          error_details?: Json | null
          id?: string
          location_id?: string | null
          records_failed?: number | null
          records_processed?: number | null
          source_name?: string
          source_type?: string
          started_at?: string
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "data_ingestion_logs_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "restaurant_locations"
            referencedColumns: ["id"]
          },
        ]
      }
      data_mappings: {
        Row: {
          approved_by: string | null
          confidence_score:
            | Database["public"]["Enums"]["confidence_level"]
            | null
          created_at: string
          data_type: Database["public"]["Enums"]["data_type"]
          id: string
          mapped_by: string | null
          master_id: string
          master_table: string
          source_system_id: string | null
          source_value: string
          status: Database["public"]["Enums"]["mapping_status"] | null
          updated_at: string
        }
        Insert: {
          approved_by?: string | null
          confidence_score?:
            | Database["public"]["Enums"]["confidence_level"]
            | null
          created_at?: string
          data_type: Database["public"]["Enums"]["data_type"]
          id?: string
          mapped_by?: string | null
          master_id: string
          master_table: string
          source_system_id?: string | null
          source_value: string
          status?: Database["public"]["Enums"]["mapping_status"] | null
          updated_at?: string
        }
        Update: {
          approved_by?: string | null
          confidence_score?:
            | Database["public"]["Enums"]["confidence_level"]
            | null
          created_at?: string
          data_type?: Database["public"]["Enums"]["data_type"]
          id?: string
          mapped_by?: string | null
          master_id?: string
          master_table?: string
          source_system_id?: string | null
          source_value?: string
          status?: Database["public"]["Enums"]["mapping_status"] | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "data_mappings_source_system_id_fkey"
            columns: ["source_system_id"]
            isOneToOne: false
            referencedRelation: "source_systems"
            referencedColumns: ["id"]
          },
        ]
      }
      data_quality_metrics: {
        Row: {
          id: string
          last_updated: string
          mapped_count: number | null
          quality_score: number | null
          table_name: string
          total_count: number | null
          unmapped_count: number | null
        }
        Insert: {
          id?: string
          last_updated?: string
          mapped_count?: number | null
          quality_score?: number | null
          table_name: string
          total_count?: number | null
          unmapped_count?: number | null
        }
        Update: {
          id?: string
          last_updated?: string
          mapped_count?: number | null
          quality_score?: number | null
          table_name?: string
          total_count?: number | null
          unmapped_count?: number | null
        }
        Relationships: []
      }
      dim_customer: {
        Row: {
          created_date: string | null
          customer_id: string | null
          customer_key: number
          customer_lifetime_value: number | null
          customer_type: string | null
          first_name: string | null
          is_active: boolean | null
          last_name: string | null
          loyalty_tier: string | null
          registration_date: string | null
          source: string | null
          tenant_key: number | null
          total_spent: number | null
          total_visits: number | null
          updated_date: string | null
        }
        Insert: {
          created_date?: string | null
          customer_id?: string | null
          customer_key?: number
          customer_lifetime_value?: number | null
          customer_type?: string | null
          first_name?: string | null
          is_active?: boolean | null
          last_name?: string | null
          loyalty_tier?: string | null
          registration_date?: string | null
          source?: string | null
          tenant_key?: number | null
          total_spent?: number | null
          total_visits?: number | null
          updated_date?: string | null
        }
        Update: {
          created_date?: string | null
          customer_id?: string | null
          customer_key?: number
          customer_lifetime_value?: number | null
          customer_type?: string | null
          first_name?: string | null
          is_active?: boolean | null
          last_name?: string | null
          loyalty_tier?: string | null
          registration_date?: string | null
          source?: string | null
          tenant_key?: number | null
          total_spent?: number | null
          total_visits?: number | null
          updated_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "dim_customer_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
        ]
      }
      dim_date: {
        Row: {
          date_key: number
          date_value: string
          day_name: string | null
          day_of_month: number | null
          day_of_week: number | null
          day_of_year: number | null
          fiscal_quarter: number | null
          fiscal_year: number | null
          is_holiday: boolean | null
          is_weekend: boolean | null
          month: number | null
          month_name: string | null
          quarter: number | null
          tenant_key: number | null
          week: number | null
          year: number | null
        }
        Insert: {
          date_key: number
          date_value: string
          day_name?: string | null
          day_of_month?: number | null
          day_of_week?: number | null
          day_of_year?: number | null
          fiscal_quarter?: number | null
          fiscal_year?: number | null
          is_holiday?: boolean | null
          is_weekend?: boolean | null
          month?: number | null
          month_name?: string | null
          quarter?: number | null
          tenant_key?: number | null
          week?: number | null
          year?: number | null
        }
        Update: {
          date_key?: number
          date_value?: string
          day_name?: string | null
          day_of_month?: number | null
          day_of_week?: number | null
          day_of_year?: number | null
          fiscal_quarter?: number | null
          fiscal_year?: number | null
          is_holiday?: boolean | null
          is_weekend?: boolean | null
          month?: number | null
          month_name?: string | null
          quarter?: number | null
          tenant_key?: number | null
          week?: number | null
          year?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "dim_date_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
        ]
      }
      dim_employee: {
        Row: {
          created_date: string | null
          department: string | null
          employee_id: string
          employee_key: number
          employee_name: string
          first_name: string | null
          hire_date: string | null
          is_active: boolean | null
          last_name: string | null
          location_key: number | null
          performance_rating: number | null
          role: string | null
          tenant_key: number | null
          updated_date: string | null
        }
        Insert: {
          created_date?: string | null
          department?: string | null
          employee_id: string
          employee_key?: number
          employee_name: string
          first_name?: string | null
          hire_date?: string | null
          is_active?: boolean | null
          last_name?: string | null
          location_key?: number | null
          performance_rating?: number | null
          role?: string | null
          tenant_key?: number | null
          updated_date?: string | null
        }
        Update: {
          created_date?: string | null
          department?: string | null
          employee_id?: string
          employee_key?: number
          employee_name?: string
          first_name?: string | null
          hire_date?: string | null
          is_active?: boolean | null
          last_name?: string | null
          location_key?: number | null
          performance_rating?: number | null
          role?: string | null
          tenant_key?: number | null
          updated_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "dim_employee_location_key_fkey"
            columns: ["location_key"]
            isOneToOne: false
            referencedRelation: "dim_location"
            referencedColumns: ["location_key"]
          },
          {
            foreignKeyName: "dim_employee_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
        ]
      }
      dim_location: {
        Row: {
          address: string | null
          city: string | null
          created_date: string | null
          drive_thru: boolean | null
          is_active: boolean | null
          location_id: string
          location_key: number
          location_name: string
          location_type: string | null
          manager_name: string | null
          phone: string | null
          region: string | null
          seating_capacity: number | null
          state: string | null
          tenant_key: number | null
          updated_date: string | null
          zip_code: string | null
        }
        Insert: {
          address?: string | null
          city?: string | null
          created_date?: string | null
          drive_thru?: boolean | null
          is_active?: boolean | null
          location_id: string
          location_key?: number
          location_name: string
          location_type?: string | null
          manager_name?: string | null
          phone?: string | null
          region?: string | null
          seating_capacity?: number | null
          state?: string | null
          tenant_key?: number | null
          updated_date?: string | null
          zip_code?: string | null
        }
        Update: {
          address?: string | null
          city?: string | null
          created_date?: string | null
          drive_thru?: boolean | null
          is_active?: boolean | null
          location_id?: string
          location_key?: number
          location_name?: string
          location_type?: string | null
          manager_name?: string | null
          phone?: string | null
          region?: string | null
          seating_capacity?: number | null
          state?: string | null
          tenant_key?: number | null
          updated_date?: string | null
          zip_code?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "dim_location_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
        ]
      }
      dim_menu_item: {
        Row: {
          allergens: string | null
          calories: number | null
          category: string | null
          cost_tier: string | null
          created_date: string | null
          is_active: boolean | null
          is_vegan: boolean | null
          is_vegetarian: boolean | null
          item_name: string
          menu_item_id: string
          menu_item_key: number
          popularity_score: number | null
          price_tier: string | null
          profit_margin: number | null
          subcategory: string | null
          tenant_key: number | null
          updated_date: string | null
        }
        Insert: {
          allergens?: string | null
          calories?: number | null
          category?: string | null
          cost_tier?: string | null
          created_date?: string | null
          is_active?: boolean | null
          is_vegan?: boolean | null
          is_vegetarian?: boolean | null
          item_name: string
          menu_item_id: string
          menu_item_key?: number
          popularity_score?: number | null
          price_tier?: string | null
          profit_margin?: number | null
          subcategory?: string | null
          tenant_key?: number | null
          updated_date?: string | null
        }
        Update: {
          allergens?: string | null
          calories?: number | null
          category?: string | null
          cost_tier?: string | null
          created_date?: string | null
          is_active?: boolean | null
          is_vegan?: boolean | null
          is_vegetarian?: boolean | null
          item_name?: string
          menu_item_id?: string
          menu_item_key?: number
          popularity_score?: number | null
          price_tier?: string | null
          profit_margin?: number | null
          subcategory?: string | null
          tenant_key?: number | null
          updated_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "dim_menu_item_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
        ]
      }
      dim_order_type: {
        Row: {
          allows_customization: boolean | null
          allows_scheduling: boolean | null
          average_service_time_minutes: number | null
          channel: string | null
          created_date: string | null
          feedback_required: boolean | null
          order_type_id: string
          order_type_key: number
          order_type_name: string
          requires_reservation: boolean | null
          service_charge_percentage: number | null
          tenant_key: number | null
          updated_date: string | null
        }
        Insert: {
          allows_customization?: boolean | null
          allows_scheduling?: boolean | null
          average_service_time_minutes?: number | null
          channel?: string | null
          created_date?: string | null
          feedback_required?: boolean | null
          order_type_id: string
          order_type_key?: number
          order_type_name: string
          requires_reservation?: boolean | null
          service_charge_percentage?: number | null
          tenant_key?: number | null
          updated_date?: string | null
        }
        Update: {
          allows_customization?: boolean | null
          allows_scheduling?: boolean | null
          average_service_time_minutes?: number | null
          channel?: string | null
          created_date?: string | null
          feedback_required?: boolean | null
          order_type_id?: string
          order_type_key?: number
          order_type_name?: string
          requires_reservation?: boolean | null
          service_charge_percentage?: number | null
          tenant_key?: number | null
          updated_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "dim_order_type_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
        ]
      }
      dim_payment_method: {
        Row: {
          created_date: string | null
          is_digital: boolean | null
          payment_method_id: string
          payment_method_key: number
          payment_provider: string | null
          payment_type: string
          processing_fee_percentage: number | null
          requires_signature: boolean | null
          tenant_key: number | null
          updated_date: string | null
        }
        Insert: {
          created_date?: string | null
          is_digital?: boolean | null
          payment_method_id: string
          payment_method_key?: number
          payment_provider?: string | null
          payment_type: string
          processing_fee_percentage?: number | null
          requires_signature?: boolean | null
          tenant_key?: number | null
          updated_date?: string | null
        }
        Update: {
          created_date?: string | null
          is_digital?: boolean | null
          payment_method_id?: string
          payment_method_key?: number
          payment_provider?: string | null
          payment_type?: string
          processing_fee_percentage?: number | null
          requires_signature?: boolean | null
          tenant_key?: number | null
          updated_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "dim_payment_method_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
        ]
      }
      dim_promotion: {
        Row: {
          channel: string | null
          coupon_code: string | null
          created_date: string | null
          discount_type: string | null
          discount_value: number | null
          end_date: string | null
          is_combinable: boolean | null
          max_discount_amount: number | null
          min_purchase_amount: number | null
          promotion_description: string | null
          promotion_id: string
          promotion_key: number
          promotion_name: string
          promotion_type: string | null
          start_date: string | null
          tenant_key: number | null
          updated_date: string | null
        }
        Insert: {
          channel?: string | null
          coupon_code?: string | null
          created_date?: string | null
          discount_type?: string | null
          discount_value?: number | null
          end_date?: string | null
          is_combinable?: boolean | null
          max_discount_amount?: number | null
          min_purchase_amount?: number | null
          promotion_description?: string | null
          promotion_id: string
          promotion_key?: number
          promotion_name: string
          promotion_type?: string | null
          start_date?: string | null
          tenant_key?: number | null
          updated_date?: string | null
        }
        Update: {
          channel?: string | null
          coupon_code?: string | null
          created_date?: string | null
          discount_type?: string | null
          discount_value?: number | null
          end_date?: string | null
          is_combinable?: boolean | null
          max_discount_amount?: number | null
          min_purchase_amount?: number | null
          promotion_description?: string | null
          promotion_id?: string
          promotion_key?: number
          promotion_name?: string
          promotion_type?: string | null
          start_date?: string | null
          tenant_key?: number | null
          updated_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "dim_promotion_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
        ]
      }
      dim_tenant: {
        Row: {
          created_date: string | null
          is_active: boolean | null
          tenant_id: string
          tenant_key: number
          tenant_name: string
          tenant_type: string | null
          updated_date: string | null
        }
        Insert: {
          created_date?: string | null
          is_active?: boolean | null
          tenant_id?: string
          tenant_key?: number
          tenant_name: string
          tenant_type?: string | null
          updated_date?: string | null
        }
        Update: {
          created_date?: string | null
          is_active?: boolean | null
          tenant_id?: string
          tenant_key?: number
          tenant_name?: string
          tenant_type?: string | null
          updated_date?: string | null
        }
        Relationships: []
      }
      dim_time: {
        Row: {
          day_part: string | null
          hour: number | null
          hour_name: string | null
          is_peak_hour: boolean | null
          minute: number | null
          tenant_key: number | null
          time_key: number
          time_value: string
        }
        Insert: {
          day_part?: string | null
          hour?: number | null
          hour_name?: string | null
          is_peak_hour?: boolean | null
          minute?: number | null
          tenant_key?: number | null
          time_key: number
          time_value: string
        }
        Update: {
          day_part?: string | null
          hour?: number | null
          hour_name?: string | null
          is_peak_hour?: boolean | null
          minute?: number | null
          tenant_key?: number | null
          time_key?: number
          time_value?: string
        }
        Relationships: [
          {
            foreignKeyName: "dim_time_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
        ]
      }
      fact_inventory: {
        Row: {
          beginning_inventory: number | null
          created_date: string | null
          date_key: number | null
          ending_inventory: number | null
          inventory_key: number
          inventory_turns: number | null
          location_key: number | null
          menu_item_key: number | null
          purchases: number | null
          stockout_risk_score: number | null
          tenant_key: number | null
          updated_date: string | null
          usage: number | null
          waste: number | null
          waste_percentage: number | null
        }
        Insert: {
          beginning_inventory?: number | null
          created_date?: string | null
          date_key?: number | null
          ending_inventory?: number | null
          inventory_key?: number
          inventory_turns?: number | null
          location_key?: number | null
          menu_item_key?: number | null
          purchases?: number | null
          stockout_risk_score?: number | null
          tenant_key?: number | null
          updated_date?: string | null
          usage?: number | null
          waste?: number | null
          waste_percentage?: number | null
        }
        Update: {
          beginning_inventory?: number | null
          created_date?: string | null
          date_key?: number | null
          ending_inventory?: number | null
          inventory_key?: number
          inventory_turns?: number | null
          location_key?: number | null
          menu_item_key?: number | null
          purchases?: number | null
          stockout_risk_score?: number | null
          tenant_key?: number | null
          updated_date?: string | null
          usage?: number | null
          waste?: number | null
          waste_percentage?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "fact_inventory_date_key_fkey"
            columns: ["date_key"]
            isOneToOne: false
            referencedRelation: "dim_date"
            referencedColumns: ["date_key"]
          },
          {
            foreignKeyName: "fact_inventory_location_key_fkey"
            columns: ["location_key"]
            isOneToOne: false
            referencedRelation: "dim_location"
            referencedColumns: ["location_key"]
          },
          {
            foreignKeyName: "fact_inventory_menu_item_key_fkey"
            columns: ["menu_item_key"]
            isOneToOne: false
            referencedRelation: "dim_menu_item"
            referencedColumns: ["menu_item_key"]
          },
          {
            foreignKeyName: "fact_inventory_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
        ]
      }
      fact_labor: {
        Row: {
          actual_hours: number | null
          break_time_minutes: number | null
          created_date: string | null
          date_key: number | null
          employee_key: number | null
          is_late: boolean | null
          is_no_show: boolean | null
          labor_cost: number | null
          labor_key: number
          location_key: number | null
          overtime_cost: number | null
          overtime_hours: number | null
          productivity_score: number | null
          scheduled_hours: number | null
          shift_type: string | null
          tenant_key: number | null
          time_key_end: number | null
          time_key_start: number | null
          total_labor_cost: number | null
          updated_date: string | null
        }
        Insert: {
          actual_hours?: number | null
          break_time_minutes?: number | null
          created_date?: string | null
          date_key?: number | null
          employee_key?: number | null
          is_late?: boolean | null
          is_no_show?: boolean | null
          labor_cost?: number | null
          labor_key?: number
          location_key?: number | null
          overtime_cost?: number | null
          overtime_hours?: number | null
          productivity_score?: number | null
          scheduled_hours?: number | null
          shift_type?: string | null
          tenant_key?: number | null
          time_key_end?: number | null
          time_key_start?: number | null
          total_labor_cost?: number | null
          updated_date?: string | null
        }
        Update: {
          actual_hours?: number | null
          break_time_minutes?: number | null
          created_date?: string | null
          date_key?: number | null
          employee_key?: number | null
          is_late?: boolean | null
          is_no_show?: boolean | null
          labor_cost?: number | null
          labor_key?: number
          location_key?: number | null
          overtime_cost?: number | null
          overtime_hours?: number | null
          productivity_score?: number | null
          scheduled_hours?: number | null
          shift_type?: string | null
          tenant_key?: number | null
          time_key_end?: number | null
          time_key_start?: number | null
          total_labor_cost?: number | null
          updated_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fact_labor_employee_key_fkey"
            columns: ["employee_key"]
            isOneToOne: false
            referencedRelation: "dim_employee"
            referencedColumns: ["employee_key"]
          },
          {
            foreignKeyName: "fact_labor_location_key_fkey"
            columns: ["location_key"]
            isOneToOne: false
            referencedRelation: "dim_location"
            referencedColumns: ["location_key"]
          },
          {
            foreignKeyName: "fact_labor_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
        ]
      }
      fact_sales: {
        Row: {
          cost_amount: number | null
          created_date: string | null
          customer_count: number | null
          customer_key: number | null
          date_key: number | null
          discount_amount: number | null
          employee_key: number | null
          gross_sales_amount: number
          location_key: number | null
          net_sales_amount: number
          order_type: string | null
          order_type_key: number | null
          payment_method: string | null
          payment_method_key: number | null
          preparation_time_minutes: number | null
          profit_amount: number | null
          promotion_key: number | null
          sales_key: number
          tax_amount: number | null
          tenant_key: number | null
          time_key: number | null
          tip_amount: number | null
          transaction_count: number | null
          transaction_id: string
          updated_date: string | null
        }
        Insert: {
          cost_amount?: number | null
          created_date?: string | null
          customer_count?: number | null
          customer_key?: number | null
          date_key?: number | null
          discount_amount?: number | null
          employee_key?: number | null
          gross_sales_amount: number
          location_key?: number | null
          net_sales_amount: number
          order_type?: string | null
          order_type_key?: number | null
          payment_method?: string | null
          payment_method_key?: number | null
          preparation_time_minutes?: number | null
          profit_amount?: number | null
          promotion_key?: number | null
          sales_key?: number
          tax_amount?: number | null
          tenant_key?: number | null
          time_key?: number | null
          tip_amount?: number | null
          transaction_count?: number | null
          transaction_id: string
          updated_date?: string | null
        }
        Update: {
          cost_amount?: number | null
          created_date?: string | null
          customer_count?: number | null
          customer_key?: number | null
          date_key?: number | null
          discount_amount?: number | null
          employee_key?: number | null
          gross_sales_amount?: number
          location_key?: number | null
          net_sales_amount?: number
          order_type?: string | null
          order_type_key?: number | null
          payment_method?: string | null
          payment_method_key?: number | null
          preparation_time_minutes?: number | null
          profit_amount?: number | null
          promotion_key?: number | null
          sales_key?: number
          tax_amount?: number | null
          tenant_key?: number | null
          time_key?: number | null
          tip_amount?: number | null
          transaction_count?: number | null
          transaction_id?: string
          updated_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fact_sales_customer_key_fkey"
            columns: ["customer_key"]
            isOneToOne: false
            referencedRelation: "dim_customer"
            referencedColumns: ["customer_key"]
          },
          {
            foreignKeyName: "fact_sales_date_key_fkey"
            columns: ["date_key"]
            isOneToOne: false
            referencedRelation: "dim_date"
            referencedColumns: ["date_key"]
          },
          {
            foreignKeyName: "fact_sales_employee_key_fkey"
            columns: ["employee_key"]
            isOneToOne: false
            referencedRelation: "dim_employee"
            referencedColumns: ["employee_key"]
          },
          {
            foreignKeyName: "fact_sales_location_key_fkey"
            columns: ["location_key"]
            isOneToOne: false
            referencedRelation: "dim_location"
            referencedColumns: ["location_key"]
          },
          {
            foreignKeyName: "fact_sales_order_type_key_fkey"
            columns: ["order_type_key"]
            isOneToOne: false
            referencedRelation: "dim_order_type"
            referencedColumns: ["order_type_key"]
          },
          {
            foreignKeyName: "fact_sales_payment_method_key_fkey"
            columns: ["payment_method_key"]
            isOneToOne: false
            referencedRelation: "dim_payment_method"
            referencedColumns: ["payment_method_key"]
          },
          {
            foreignKeyName: "fact_sales_promotion_key_fkey"
            columns: ["promotion_key"]
            isOneToOne: false
            referencedRelation: "dim_promotion"
            referencedColumns: ["promotion_key"]
          },
          {
            foreignKeyName: "fact_sales_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
          {
            foreignKeyName: "fact_sales_time_key_fkey"
            columns: ["time_key"]
            isOneToOne: false
            referencedRelation: "dim_time"
            referencedColumns: ["time_key"]
          },
        ]
      }
      fact_sales_line_item: {
        Row: {
          created_date: string | null
          line_cost: number | null
          line_item_key: number
          line_total: number
          menu_item_key: number | null
          quantity: number
          sales_key: number | null
          unit_cost: number | null
          unit_price: number
        }
        Insert: {
          created_date?: string | null
          line_cost?: number | null
          line_item_key?: number
          line_total: number
          menu_item_key?: number | null
          quantity: number
          sales_key?: number | null
          unit_cost?: number | null
          unit_price: number
        }
        Update: {
          created_date?: string | null
          line_cost?: number | null
          line_item_key?: number
          line_total?: number
          menu_item_key?: number | null
          quantity?: number
          sales_key?: number | null
          unit_cost?: number | null
          unit_price?: number
        }
        Relationships: [
          {
            foreignKeyName: "fact_sales_line_item_menu_item_key_fkey"
            columns: ["menu_item_key"]
            isOneToOne: false
            referencedRelation: "dim_menu_item"
            referencedColumns: ["menu_item_key"]
          },
          {
            foreignKeyName: "fact_sales_line_item_sales_key_fkey"
            columns: ["sales_key"]
            isOneToOne: false
            referencedRelation: "fact_sales"
            referencedColumns: ["sales_key"]
          },
        ]
      }
      integration_status: {
        Row: {
          config: Json | null
          created_at: string
          error_message: string | null
          id: string
          integration_name: string
          integration_type: string
          last_sync_at: string | null
          location_id: string | null
          next_sync_at: string | null
          status: string
          sync_frequency: string | null
          updated_at: string
        }
        Insert: {
          config?: Json | null
          created_at?: string
          error_message?: string | null
          id?: string
          integration_name: string
          integration_type: string
          last_sync_at?: string | null
          location_id?: string | null
          next_sync_at?: string | null
          status?: string
          sync_frequency?: string | null
          updated_at?: string
        }
        Update: {
          config?: Json | null
          created_at?: string
          error_message?: string | null
          id?: string
          integration_name?: string
          integration_type?: string
          last_sync_at?: string | null
          location_id?: string | null
          next_sync_at?: string | null
          status?: string
          sync_frequency?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "integration_status_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "restaurant_locations"
            referencedColumns: ["id"]
          },
        ]
      }
      mapping_history: {
        Row: {
          changed_at: string
          changed_by: string | null
          id: string
          mapping_id: string | null
          new_value: Json | null
          old_value: Json | null
        }
        Insert: {
          changed_at?: string
          changed_by?: string | null
          id?: string
          mapping_id?: string | null
          new_value?: Json | null
          old_value?: Json | null
        }
        Update: {
          changed_at?: string
          changed_by?: string | null
          id?: string
          mapping_id?: string | null
          new_value?: Json | null
          old_value?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "mapping_history_mapping_id_fkey"
            columns: ["mapping_id"]
            isOneToOne: false
            referencedRelation: "data_mappings"
            referencedColumns: ["id"]
          },
        ]
      }
      mapping_rules: {
        Row: {
          confidence: Database["public"]["Enums"]["confidence_level"] | null
          created_at: string
          created_by: string | null
          id: string
          is_active: boolean | null
          rule_type: string | null
          source_pattern: string
          target_field: string
          target_table: string
          updated_at: string
        }
        Insert: {
          confidence?: Database["public"]["Enums"]["confidence_level"] | null
          created_at?: string
          created_by?: string | null
          id?: string
          is_active?: boolean | null
          rule_type?: string | null
          source_pattern: string
          target_field: string
          target_table: string
          updated_at?: string
        }
        Update: {
          confidence?: Database["public"]["Enums"]["confidence_level"] | null
          created_at?: string
          created_by?: string | null
          id?: string
          is_active?: boolean | null
          rule_type?: string | null
          source_pattern?: string
          target_field?: string
          target_table?: string
          updated_at?: string
        }
        Relationships: []
      }
      master_day_parts: {
        Row: {
          created_at: string
          description: string | null
          end_time: string | null
          id: string
          name: string
          owner_id: string | null
          start_time: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          end_time?: string | null
          id?: string
          name: string
          owner_id?: string | null
          start_time?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          end_time?: string | null
          id?: string
          name?: string
          owner_id?: string | null
          start_time?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      master_employees: {
        Row: {
          created_at: string
          department: string | null
          id: string
          location_id: string | null
          name: string
          owner_id: string | null
          role: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          department?: string | null
          id?: string
          location_id?: string | null
          name: string
          owner_id?: string | null
          role?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          department?: string | null
          id?: string
          location_id?: string | null
          name?: string
          owner_id?: string | null
          role?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "master_employees_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "master_locations"
            referencedColumns: ["id"]
          },
        ]
      }
      master_ingredients_supplies: {
        Row: {
          category: string | null
          created_at: string
          id: string
          name: string
          owner_id: string | null
          supplier_info: Json | null
          unit: string | null
          updated_at: string
        }
        Insert: {
          category?: string | null
          created_at?: string
          id?: string
          name: string
          owner_id?: string | null
          supplier_info?: Json | null
          unit?: string | null
          updated_at?: string
        }
        Update: {
          category?: string | null
          created_at?: string
          id?: string
          name?: string
          owner_id?: string | null
          supplier_info?: Json | null
          unit?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      master_locations: {
        Row: {
          address: string | null
          city: string | null
          created_at: string
          id: string
          location_type: string | null
          name: string
          owner_id: string | null
          region: string | null
          state: string | null
          updated_at: string
        }
        Insert: {
          address?: string | null
          city?: string | null
          created_at?: string
          id?: string
          location_type?: string | null
          name: string
          owner_id?: string | null
          region?: string | null
          state?: string | null
          updated_at?: string
        }
        Update: {
          address?: string | null
          city?: string | null
          created_at?: string
          id?: string
          location_type?: string | null
          name?: string
          owner_id?: string | null
          region?: string | null
          state?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      master_menu_categories: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
          owner_id: string | null
          parent_category_id: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
          owner_id?: string | null
          parent_category_id?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          owner_id?: string | null
          parent_category_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "master_menu_categories_parent_category_id_fkey"
            columns: ["parent_category_id"]
            isOneToOne: false
            referencedRelation: "master_menu_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      master_menu_items: {
        Row: {
          category: string | null
          created_at: string
          description: string | null
          id: string
          name: string
          owner_id: string | null
          price_range: string | null
          updated_at: string
        }
        Insert: {
          category?: string | null
          created_at?: string
          description?: string | null
          id?: string
          name: string
          owner_id?: string | null
          price_range?: string | null
          updated_at?: string
        }
        Update: {
          category?: string | null
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          owner_id?: string | null
          price_range?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      master_restaurant_operators: {
        Row: {
          contact_info: Json | null
          created_at: string
          id: string
          name: string
          owner_id: string | null
          regions: string[] | null
          updated_at: string
        }
        Insert: {
          contact_info?: Json | null
          created_at?: string
          id?: string
          name: string
          owner_id?: string | null
          regions?: string[] | null
          updated_at?: string
        }
        Update: {
          contact_info?: Json | null
          created_at?: string
          id?: string
          name?: string
          owner_id?: string | null
          regions?: string[] | null
          updated_at?: string
        }
        Relationships: []
      }
      master_survey_questions: {
        Row: {
          category: string | null
          created_at: string
          id: string
          owner_id: string | null
          question_text: string
          response_type: string | null
          updated_at: string
        }
        Insert: {
          category?: string | null
          created_at?: string
          id?: string
          owner_id?: string | null
          question_text: string
          response_type?: string | null
          updated_at?: string
        }
        Update: {
          category?: string | null
          created_at?: string
          id?: string
          owner_id?: string | null
          question_text?: string
          response_type?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      menu_items: {
        Row: {
          category: string | null
          cost: number | null
          created_at: string
          description: string | null
          id: string
          is_active: boolean | null
          location_id: string | null
          name: string
          pos_item_id: string | null
          price: number | null
          source_system: string | null
          updated_at: string
        }
        Insert: {
          category?: string | null
          cost?: number | null
          created_at?: string
          description?: string | null
          id?: string
          is_active?: boolean | null
          location_id?: string | null
          name: string
          pos_item_id?: string | null
          price?: number | null
          source_system?: string | null
          updated_at?: string
        }
        Update: {
          category?: string | null
          cost?: number | null
          created_at?: string
          description?: string | null
          id?: string
          is_active?: boolean | null
          location_id?: string | null
          name?: string
          pos_item_id?: string | null
          price?: number | null
          source_system?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "menu_items_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "restaurant_locations"
            referencedColumns: ["id"]
          },
        ]
      }
      predictive_analytics: {
        Row: {
          actual_value: number | null
          confidence_interval_lower: number | null
          confidence_interval_upper: number | null
          confidence_level: number | null
          created_date: string | null
          feature_importance: Json | null
          forecast_date: string
          location_key: number | null
          model_name: string | null
          model_version: string | null
          predicted_value: number | null
          prediction_accuracy: number | null
          prediction_id: string
          prediction_key: number
          prediction_period: string
          prediction_type: string
          target_metric: string
          tenant_key: number | null
          updated_date: string | null
        }
        Insert: {
          actual_value?: number | null
          confidence_interval_lower?: number | null
          confidence_interval_upper?: number | null
          confidence_level?: number | null
          created_date?: string | null
          feature_importance?: Json | null
          forecast_date: string
          location_key?: number | null
          model_name?: string | null
          model_version?: string | null
          predicted_value?: number | null
          prediction_accuracy?: number | null
          prediction_id?: string
          prediction_key?: number
          prediction_period: string
          prediction_type: string
          target_metric: string
          tenant_key?: number | null
          updated_date?: string | null
        }
        Update: {
          actual_value?: number | null
          confidence_interval_lower?: number | null
          confidence_interval_upper?: number | null
          confidence_level?: number | null
          created_date?: string | null
          feature_importance?: Json | null
          forecast_date?: string
          location_key?: number | null
          model_name?: string | null
          model_version?: string | null
          predicted_value?: number | null
          prediction_accuracy?: number | null
          prediction_id?: string
          prediction_key?: number
          prediction_period?: string
          prediction_type?: string
          target_metric?: string
          tenant_key?: number | null
          updated_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "predictive_analytics_location_key_fkey"
            columns: ["location_key"]
            isOneToOne: false
            referencedRelation: "dim_location"
            referencedColumns: ["location_key"]
          },
          {
            foreignKeyName: "predictive_analytics_tenant_key_fkey"
            columns: ["tenant_key"]
            isOneToOne: false
            referencedRelation: "dim_tenant"
            referencedColumns: ["tenant_key"]
          },
        ]
      }
      profiles: {
        Row: {
          company_id: string | null
          created_at: string | null
          email_alerts: boolean | null
          full_name: string | null
          has_completed_onboarding: boolean | null
          id: string
          invited_by: string | null
          onboarding_step: number | null
          phone: string | null
          role: string | null
          sms_alerts: boolean | null
          timezone: string | null
          updated_at: string | null
        }
        Insert: {
          company_id?: string | null
          created_at?: string | null
          email_alerts?: boolean | null
          full_name?: string | null
          has_completed_onboarding?: boolean | null
          id: string
          invited_by?: string | null
          onboarding_step?: number | null
          phone?: string | null
          role?: string | null
          sms_alerts?: boolean | null
          timezone?: string | null
          updated_at?: string | null
        }
        Update: {
          company_id?: string | null
          created_at?: string | null
          email_alerts?: boolean | null
          full_name?: string | null
          has_completed_onboarding?: boolean | null
          id?: string
          invited_by?: string | null
          onboarding_step?: number | null
          phone?: string | null
          role?: string | null
          sms_alerts?: boolean | null
          timezone?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      restaurant_locations: {
        Row: {
          address: string | null
          city: string | null
          created_at: string
          id: string
          manager_name: string | null
          name: string
          phone: string | null
          state: string | null
          status: string | null
          updated_at: string
          zip_code: string | null
        }
        Insert: {
          address?: string | null
          city?: string | null
          created_at?: string
          id?: string
          manager_name?: string | null
          name: string
          phone?: string | null
          state?: string | null
          status?: string | null
          updated_at?: string
          zip_code?: string | null
        }
        Update: {
          address?: string | null
          city?: string | null
          created_at?: string
          id?: string
          manager_name?: string | null
          name?: string
          phone?: string | null
          state?: string | null
          status?: string | null
          updated_at?: string
          zip_code?: string | null
        }
        Relationships: []
      }
      sales_transactions: {
        Row: {
          created_at: string
          customer_count: number | null
          id: string
          location_id: string | null
          order_type: string | null
          payment_method: string | null
          pos_transaction_id: string | null
          source_system: string | null
          tax_amount: number | null
          tip_amount: number | null
          total_amount: number
          transaction_date: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          customer_count?: number | null
          id?: string
          location_id?: string | null
          order_type?: string | null
          payment_method?: string | null
          pos_transaction_id?: string | null
          source_system?: string | null
          tax_amount?: number | null
          tip_amount?: number | null
          total_amount: number
          transaction_date: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          customer_count?: number | null
          id?: string
          location_id?: string | null
          order_type?: string | null
          payment_method?: string | null
          pos_transaction_id?: string | null
          source_system?: string | null
          tax_amount?: number | null
          tip_amount?: number | null
          total_amount?: number
          transaction_date?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "sales_transactions_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "restaurant_locations"
            referencedColumns: ["id"]
          },
        ]
      }
      source_systems: {
        Row: {
          api_endpoint: string | null
          auth_config: Json | null
          created_at: string
          id: string
          is_active: boolean | null
          name: string
          system_type: string
          updated_at: string
        }
        Insert: {
          api_endpoint?: string | null
          auth_config?: Json | null
          created_at?: string
          id?: string
          is_active?: boolean | null
          name: string
          system_type: string
          updated_at?: string
        }
        Update: {
          api_endpoint?: string | null
          auth_config?: Json | null
          created_at?: string
          id?: string
          is_active?: boolean | null
          name?: string
          system_type?: string
          updated_at?: string
        }
        Relationships: []
      }
      team_invitations: {
        Row: {
          accepted_at: string | null
          company_id: string
          created_at: string
          email: string
          expires_at: string
          id: string
          invitation_token: string
          invited_by: string | null
          role: string
          status: string | null
        }
        Insert: {
          accepted_at?: string | null
          company_id: string
          created_at?: string
          email: string
          expires_at?: string
          id?: string
          invitation_token?: string
          invited_by?: string | null
          role: string
          status?: string | null
        }
        Update: {
          accepted_at?: string | null
          company_id?: string
          created_at?: string
          email?: string
          expires_at?: string
          id?: string
          invitation_token?: string
          invited_by?: string | null
          role?: string
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "team_invitations_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      transaction_line_items: {
        Row: {
          created_at: string
          id: string
          item_name: string
          menu_item_id: string | null
          modifiers: Json | null
          quantity: number
          total_price: number
          transaction_id: string | null
          unit_price: number
        }
        Insert: {
          created_at?: string
          id?: string
          item_name: string
          menu_item_id?: string | null
          modifiers?: Json | null
          quantity?: number
          total_price: number
          transaction_id?: string | null
          unit_price: number
        }
        Update: {
          created_at?: string
          id?: string
          item_name?: string
          menu_item_id?: string | null
          modifiers?: Json | null
          quantity?: number
          total_price?: number
          transaction_id?: string | null
          unit_price?: number
        }
        Relationships: [
          {
            foreignKeyName: "transaction_line_items_menu_item_id_fkey"
            columns: ["menu_item_id"]
            isOneToOne: false
            referencedRelation: "menu_items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transaction_line_items_transaction_id_fkey"
            columns: ["transaction_id"]
            isOneToOne: false
            referencedRelation: "sales_transactions"
            referencedColumns: ["id"]
          },
        ]
      }
      unmapped_items: {
        Row: {
          created_at: string
          data_type: Database["public"]["Enums"]["data_type"]
          frequency: number | null
          id: string
          last_seen: string
          metadata: Json | null
          source_system_id: string | null
          source_value: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          data_type: Database["public"]["Enums"]["data_type"]
          frequency?: number | null
          id?: string
          last_seen?: string
          metadata?: Json | null
          source_system_id?: string | null
          source_value: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          data_type?: Database["public"]["Enums"]["data_type"]
          frequency?: number | null
          id?: string
          last_seen?: string
          metadata?: Json | null
          source_system_id?: string | null
          source_value?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "unmapped_items_source_system_id_fkey"
            columns: ["source_system_id"]
            isOneToOne: false
            referencedRelation: "source_systems"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_user_tenant_key: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      is_user_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
    }
    Enums: {
      confidence_level: "low" | "medium" | "high"
      data_type:
        | "menu-items"
        | "menu-categories"
        | "menu-products"
        | "day-parts"
        | "food-sales"
        | "locations"
        | "employees"
        | "restaurant-operators"
        | "ingredients-supplies"
        | "survey-questions"
        | "dates"
        | "unmapped-items"
      mapping_status: "pending" | "approved" | "rejected" | "auto-mapped"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      confidence_level: ["low", "medium", "high"],
      data_type: [
        "menu-items",
        "menu-categories",
        "menu-products",
        "day-parts",
        "food-sales",
        "locations",
        "employees",
        "restaurant-operators",
        "ingredients-supplies",
        "survey-questions",
        "dates",
        "unmapped-items",
      ],
      mapping_status: ["pending", "approved", "rejected", "auto-mapped"],
    },
  },
} as const
