
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 24.6 95% 53.1%; /* Orange primary */
    --primary-foreground: 0 0% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 24.6 95% 53.1%; /* Orange accent */
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 24.6 95% 53.1%; /* Orange ring */
    --radius: 0.5rem;
    --chart-1: 24.6 95% 53.1%; /* Orange chart colors */
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 24.6 95% 53.1%; /* Orange primary for dark mode */
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 24.6 95% 53.1%; /* Orange accent for dark mode */
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 24.6 95% 53.1%; /* Orange ring for dark mode */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: "Inter", sans-serif;
  }
}

/* Brand-specific styling */
.convx-brand {
  background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
}

.convx-text {
  background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Walkthrough highlight ring glow animation */
@keyframes walkthrough-glow {
  0% { 
    box-shadow: 0 0 0 8px rgba(234, 88, 12, 0.3), 0 0 30px rgba(234, 88, 12, 0.6); 
  }
  100% { 
    box-shadow: 0 0 0 12px rgba(234, 88, 12, 0.1), 0 0 40px rgba(234, 88, 12, 0.8); 
  }
}

.walkthrough-highlight-ring {
  animation: pulse 1.5s infinite, walkthrough-glow 2s infinite alternate;
}

/* Walkthrough spotlight overlay */
.walkthrough-spotlight {
  background: radial-gradient(circle at center, transparent 100px, rgba(0, 0, 0, 0.8) 150px);
}

/* Enhanced walkthrough card animations */
@keyframes walkthrough-bounce-in {
  0% {
    transform: scale(0.3) rotate(-5deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.05) rotate(1deg);
  }
  70% {
    transform: scale(0.95) rotate(-0.5deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

.walkthrough-card-enter {
  animation: walkthrough-bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Arrow pulse animation */
@keyframes arrow-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.walkthrough-arrow {
  animation: arrow-pulse 1.5s infinite;
}

/* Print styles for invoice */
@media print {
  .no-print {
    display: none !important;
  }
  .invoice-container {
    box-shadow: none !important;
    border: none !important;
  }
}
