
import { supabase } from '@/integrations/supabase/client';
import { AnomalyDetection } from '@/types/aiAnalytics';

export class AnomalyService {
  static async getAnomalyDetections(tenantKey: number = 1, locationKey?: number): Promise<AnomalyDetection[]> {
    console.log('Fetching anomaly detections...');
    
    let query = supabase
      .from('anomaly_detection')
      .select(`
        *,
        dim_location(location_name)
      `)
      .eq('tenant_key', tenantKey)
      .order('created_date', { ascending: false });

    if (locationKey) {
      query = query.eq('location_key', locationKey);
    }

    const { data, error } = await query.limit(30);

    if (error) {
      console.error('Error fetching anomaly detections:', error);
      throw error;
    }

    // Cast the raw data to proper types
    return (data || []).map(item => ({
      ...item,
      threshold_breached: item.threshold_breached as 'upper' | 'lower' | 'both',
      is_confirmed: item.is_confirmed ?? false
    }));
  }

  static async confirmAnomaly(anomalyKey: number, userId?: string): Promise<boolean> {
    console.log('Confirming anomaly:', anomalyKey);
    
    const { error } = await supabase
      .from('anomaly_detection')
      .update({
        is_confirmed: true,
        confirmed_by: userId,
        confirmed_at: new Date().toISOString()
      })
      .eq('anomaly_key', anomalyKey);

    if (error) {
      console.error('Error confirming anomaly:', anomalyKey);
      return false;
    }

    return true;
  }
}
