
import { supabase } from '@/integrations/supabase/client';
import { BIMetrics } from '@/types/aiAnalytics';

export class MetricsService {
  static async getBIMetrics(tenantKey: number = 1, category?: string): Promise<BIMetrics[]> {
    console.log('Fetching BI metrics...');
    
    let query = supabase
      .from('bi_metrics')
      .select('*')
      .eq('tenant_key', tenantKey)
      .order('created_date', { ascending: false });

    if (category) {
      query = query.eq('metric_category', category);
    }

    const { data, error } = await query.limit(100);

    if (error) {
      console.error('Error fetching BI metrics:', error);
      throw error;
    }

    // Cast the raw data to proper types
    return (data || []).map(item => ({
      ...item,
      metric_category: item.metric_category as 'financial' | 'operational' | 'customer' | 'strategic',
      trend_direction: item.trend_direction as 'up' | 'down' | 'stable'
    }));
  }
}
