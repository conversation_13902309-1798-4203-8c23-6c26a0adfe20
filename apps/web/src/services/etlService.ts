
// Legacy ETL Service - now delegates to the new modular services
// This file maintains backward compatibility

import { ETLOrchestrator } from './etl/etlOrchestrator';

export class ETLService {
  // Delegate to new modular services while maintaining the same API
  static async syncLocations() {
    return ETLOrchestrator.LocationService.syncLocations();
  }

  static async syncMenuItems() {
    return ETLOrchestrator.MenuItemService.syncMenuItems();
  }

  static async syncEmployees() {
    return ETLOrchestrator.EmployeeService.syncEmployees();
  }

  static async syncSalesTransactions(startDate?: string, endDate?: string) {
    return ETLOrchestrator.SalesService.syncSalesTransactions(startDate, endDate);
  }

  static async runFullETL() {
    return ETLOrchestrator.runFullETL();
  }
}
