
import { supabase } from '@/integrations/supabase/client';
import { BaseETLService } from './baseETLService';

export class LocationETLService extends BaseETLService {
  static async syncLocations() {
    console.log('Starting location sync...');
    
    try {
      // Get all locations from operational table
      const { data: locations } = await supabase
        .from('restaurant_locations')
        .select('*');

      if (!locations) return this.createETLResult(false, 0, 'No locations found');

      // Upsert into dim_location
      const dimLocations = locations.map(loc => ({
        location_id: loc.id,
        location_name: loc.name,
        address: loc.address,
        city: loc.city,
        state: loc.state,
        region: loc.state, // Using state as region for now
        location_type: 'restaurant',
        is_active: loc.status === 'active'
      }));

      const { error } = await supabase
        .from('dim_location')
        .upsert(dimLocations, { onConflict: 'location_id' });

      if (error) throw error;

      console.log(`Synced ${locations.length} locations to data warehouse`);
      return this.createETLResult(true, locations.length);
    } catch (error) {
      console.error('Location sync failed:', error);
      return this.createETLResult(false, 0, error.message);
    }
  }
}
