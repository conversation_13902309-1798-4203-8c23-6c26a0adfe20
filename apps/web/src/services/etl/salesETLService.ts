
import { supabase } from '@/integrations/supabase/client';
import { BaseETLService } from './baseETLService';

export class SalesETLService extends BaseETLService {
  static async syncSalesTransactions(startDate?: string, endDate?: string) {
    console.log('Starting enhanced sales transaction sync...');
    
    try {
      let query = supabase
        .from('sales_transactions')
        .select(`
          *,
          transaction_line_items(*)
        `);

      if (startDate) {
        query = query.gte('transaction_date', startDate);
      }
      if (endDate) {
        query = query.lte('transaction_date', endDate);
      }

      const { data: transactions } = await query;

      if (!transactions) return this.createETLResult(false, 0, 'No transactions found');

      // Get enhanced dimension mappings
      const [locationMappings, menuMappings, dateMappings, timeMappings, orderTypeMappings, paymentMappings] = await Promise.all([
        supabase.from('dim_location').select('location_key, location_id'),
        supabase.from('dim_menu_item').select('menu_item_key, menu_item_id'),
        supabase.from('dim_date').select('date_key, date_value'),
        supabase.from('dim_time').select('time_key, time_value'),
        supabase.from('dim_order_type').select('order_type_key, order_type_id'),
        supabase.from('dim_payment_method').select('payment_method_key, payment_method_id')
      ]);

      const locationMap = new Map(locationMappings.data?.map(l => [l.location_id, l.location_key]) || []);
      const menuMap = new Map(menuMappings.data?.map(m => [m.menu_item_id, m.menu_item_key]) || []);
      const dateMap = new Map(dateMappings.data?.map(d => [d.date_value, d.date_key]) || []);
      const timeMap = new Map(timeMappings.data?.map(t => [t.time_value, t.time_key]) || []);
      const orderTypeMap = new Map(orderTypeMappings.data?.map(o => [o.order_type_id, o.order_type_key]) || []);
      const paymentMap = new Map(paymentMappings.data?.map(p => [p.payment_method_id, p.payment_method_key]) || []);

      let processedCount = 0;

      for (const transaction of transactions) {
        const transactionDate = new Date(transaction.transaction_date);
        const dateKey = dateMap.get(transactionDate.toISOString().split('T')[0]);
        
        // Find closest time key (rounded to 15-minute intervals)
        const timeString = transactionDate.toTimeString().substring(0, 5);
        const [hours, minutes] = timeString.split(':').map(Number);
        const roundedMinutes = Math.floor(minutes / 15) * 15;
        const timeKey = hours * 100 + roundedMinutes;

        // Map order type and payment method
        const orderTypeKey = orderTypeMap.get(transaction.order_type?.toLowerCase().replace(' ', '_') || 'dine_in');
        const paymentMethodKey = paymentMap.get(transaction.payment_method?.toLowerCase().replace(' ', '_') || 'cash');

        const factSale = {
          tenant_key: 1, // Default tenant
          transaction_id: transaction.id,
          location_key: locationMap.get(transaction.location_id),
          date_key: dateKey,
          time_key: timeKey,
          order_type_key: orderTypeKey,
          payment_method_key: paymentMethodKey,
          customer_key: null, // Will be enhanced later
          employee_key: null, // Will be enhanced later
          gross_sales_amount: transaction.total_amount,
          net_sales_amount: transaction.total_amount - (transaction.tax_amount || 0),
          tax_amount: transaction.tax_amount || 0,
          tip_amount: transaction.tip_amount || 0,
          discount_amount: 0, // Will be enhanced later
          transaction_count: 1,
          customer_count: transaction.customer_count || 1,
          order_type: transaction.order_type,
          payment_method: transaction.payment_method,
          // Enhanced fields
          cost_amount: transaction.total_amount * 0.35, // Mock 35% cost
          profit_amount: transaction.total_amount * 0.15, // Mock 15% profit
          preparation_time_minutes: Math.floor(Math.random() * 30) + 10, // Mock prep time
          order_placed_timestamp: transaction.transaction_date,
          order_completed_timestamp: new Date(new Date(transaction.transaction_date).getTime() + 20 * 60000) // +20 mins
        };

        // Insert enhanced fact sale
        const { data: factSaleResult, error: saleError } = await supabase
          .from('fact_sales')
          .upsert(factSale, { onConflict: 'transaction_id' })
          .select('sales_key')
          .single();

        if (saleError) {
          console.error('Error inserting fact sale:', saleError);
          continue;
        }

        // Insert line items with enhanced data
        if (transaction.transaction_line_items && factSaleResult) {
          const lineItems = transaction.transaction_line_items.map(item => ({
            sales_key: factSaleResult.sales_key,
            menu_item_key: menuMap.get(item.menu_item_id),
            quantity: item.quantity,
            unit_price: item.unit_price,
            line_total: item.total_price,
            unit_cost: item.unit_price * 0.35, // Mock 35% cost
            line_cost: item.total_price * 0.35
          }));

          const { error: lineError } = await supabase
            .from('fact_sales_line_item')
            .upsert(lineItems);

          if (lineError) {
            console.error('Error inserting line items:', lineError);
          }
        }

        processedCount++;
      }

      console.log(`Synced ${processedCount} enhanced sales transactions to data warehouse`);
      return this.createETLResult(true, processedCount);
    } catch (error) {
      console.error('Enhanced sales transaction sync failed:', error);
      return this.createETLResult(false, 0, error.message);
    }
  }
}
