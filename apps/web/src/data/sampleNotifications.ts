
import { Notification } from "@/types/notification";

export const sampleNotifications: Notification[] = [
  {
    id: "1",
    type: "new_item",
    title: "New Item Detected",
    description: "A new food item has been detected in your system and needs categorization.",
    itemName: "Artisan Sourdough Bread",
    itemImage: "/placeholder.svg",
    timestamp: new Date(),
    isRead: false
  },
  {
    id: "2", 
    type: "new_item",
    title: "New Item Detected",
    description: "A new beverage item has been detected and requires attention.",
    itemName: "Cold Brew Coffee",
    itemImage: "/placeholder.svg",
    timestamp: new Date(Date.now() - 30 * 60000), // 30 minutes ago
    isRead: false
  },
  {
    id: "3",
    type: "alert",
    title: "System Alert",
    description: "Multiple new items detected in the last hour. Review recommended.",
    timestamp: new Date(Date.now() - 60 * 60000), // 1 hour ago
    isRead: false
  }
];
