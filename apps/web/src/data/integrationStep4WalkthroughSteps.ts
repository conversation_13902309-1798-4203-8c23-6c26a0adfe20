
import { WalkthroughStep } from '@/types/walkthrough';

export const integrationStep4WalkthroughSteps: WalkthroughStep[] = [
  {
    id: 'data-selection',
    title: 'Choose Data to Sync',
    description: 'Select which types of data you want to import and how often. Start with essential data like sales or customer information, then add more as needed.',
    target: '[data-tour="data-selection"]',
    position: 'top',
    highlight: true
  },
  {
    id: 'sync-frequency',
    title: 'Set Sync Frequency',
    description: 'Choose how often data should be updated. More frequent syncing gives you real-time insights but uses more resources. Most businesses start with hourly or daily syncing.',
    target: '[data-tour="sync-frequency"]',
    position: 'right',
    highlight: true
  },
  {
    id: 'sync-settings',
    title: 'Configure Sync Settings',
    description: 'Set batch sizes and error handling preferences. Default settings work for most integrations, but you can adjust them based on your data volume and quality requirements.',
    target: '[data-tour="sync-settings"]',
    position: 'top',
    highlight: true
  }
];
