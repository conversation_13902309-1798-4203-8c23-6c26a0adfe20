
import { WalkthroughStep } from '@/types/walkthrough';

export const integrationStep1WalkthroughSteps: WalkthroughStep[] = [
  {
    id: 'wizard-header',
    title: 'Welcome to Integration Setup',
    description: 'This wizard will guide you through connecting a new data source to your platform. We\'ll help you choose the right integration type, configure it properly, and start syncing data.',
    target: '[data-tour="wizard-header"]',
    position: 'bottom',
    highlight: true
  },
  {
    id: 'progress-steps',
    title: 'Track Your Progress',
    description: 'Follow your progress through the 5-step setup process. Each step builds on the previous one to ensure your integration is configured correctly and securely.',
    target: '[data-tour="progress-steps"]',
    position: 'bottom',
    highlight: true
  },
  {
    id: 'integration-categories',
    title: 'Choose Integration Category',
    description: 'Start by selecting the type of system you want to connect. Categories are organized by function - POS systems for sales data, accounting for financial data, marketing for customer insights, etc.',
    target: '[data-tour="integration-categories"]',
    position: 'top',
    highlight: true
  },
  {
    id: 'integration-features',
    title: 'Review Integration Features',
    description: 'Each integration shows what data it can provide. Look for features that match your business needs - sales data, customer information, inventory tracking, etc.',
    target: '[data-tour="integration-features"]',
    position: 'left',
    highlight: true
  }
];
