
import { WalkthroughStep } from '@/types/walkthrough';

export const integrationsWalkthroughSteps: WalkthroughStep[] = [
  {
    id: 'integrations-header',
    title: 'Welcome to Integration Pipelines',
    description: 'This is your central hub for managing all data connections. Monitor real-time status, sync frequencies, and pipeline health across all your integrated systems.',
    target: '[data-tour="integrations-header"]',
    position: 'bottom',
    highlight: true
  },
  {
    id: 'integration-stats',
    title: 'Pipeline Health Overview',
    description: 'Get a quick snapshot of your data ecosystem - active integrations, recent sync activity, and success rates. These metrics help you monitor overall system health.',
    target: '[data-tour="integration-stats"]',
    position: 'bottom',
    highlight: true
  },
  {
    id: 'add-integration-btn',
    title: 'Add New Integration',
    description: 'Connect new data sources like POS systems, marketing platforms, or payment processors. Our wizard guides you through configuration, testing, and data mapping.',
    target: '[data-tour="add-integration"]',
    position: 'left',
    highlight: true
  },
  {
    id: 'upload-data-btn',
    title: 'Manual Data Upload',
    description: 'Upload CSV files or one-time data imports when you need to add historical data or information from systems without direct API connections.',
    target: '[data-tour="upload-data"]',
    position: 'left',
    highlight: true
  },
  {
    id: 'data-uploads-table',
    title: 'Recent Upload History',
    description: 'Track all manual data uploads and their processing status. Monitor record counts, identify any failures, and ensure data quality standards.',
    target: '[data-tour="data-uploads"]',
    position: 'top',
    highlight: true
  },
  {
    id: 'integrations-table',
    title: 'Active Integration Management',
    description: 'Monitor all your live data connections in real-time. See sync frequencies, activity patterns, and quickly identify any issues that need attention.',
    target: '[data-tour="integrations-table"]',
    position: 'top',
    highlight: true
  },
  {
    id: 'integration-types',
    title: 'Integration Types',
    description: 'Different badge colors indicate integration types: Ingest (blue) brings data in, Publish (green) sends data out, Transform (purple) processes data, and Admin (gray) manages system functions.',
    target: '[data-tour="integration-types"]',
    position: 'right',
    highlight: true
  },
  {
    id: 'activity-bars',
    title: 'Real-time Activity Monitoring',
    description: 'These activity bars show sync patterns over time. Green bars indicate successful syncs, helping you visualize data flow consistency and identify any gaps.',
    target: '[data-tour="activity-bars"]',
    position: 'right',
    highlight: true
  },
  {
    id: 'integration-actions',
    title: 'Integration Controls',
    description: 'Manage each integration with these controls: Settings to modify configuration, Play to trigger manual sync, and Delete to remove the integration.',
    target: '[data-tour="integration-actions"]',
    position: 'left',
    highlight: true
  }
];
