
import { WalkthroughStep } from '@/types/walkthrough';

export const addIntegrationWalkthroughSteps: WalkthroughStep[] = [
  {
    id: 'wizard-header',
    title: 'Welcome to Integration Setup',
    description: 'This wizard will guide you through connecting a new data source to your platform. We\'ll help you choose the right integration type, configure it properly, and start syncing data.',
    target: '[data-tour="wizard-header"]',
    position: 'bottom',
    highlight: true
  },
  {
    id: 'progress-steps',
    title: 'Track Your Progress',
    description: 'Follow your progress through the 5-step setup process. Each step builds on the previous one to ensure your integration is configured correctly and securely.',
    target: '[data-tour="progress-steps"]',
    position: 'bottom',
    highlight: true
  },
  {
    id: 'integration-categories',
    title: 'Choose Integration Category',
    description: 'Start by selecting the type of system you want to connect. Categories are organized by function - POS systems for sales data, accounting for financial data, marketing for customer insights, etc.',
    target: '[data-tour="integration-categories"]',
    position: 'top',
    highlight: true
  },
  {
    id: 'integration-features',
    title: 'Review Integration Features',
    description: 'Each integration shows what data it can provide. Look for features that match your business needs - sales data, customer information, inventory tracking, etc.',
    target: '[data-tour="integration-features"]',
    position: 'left',
    highlight: true
  },
  {
    id: 'configuration-form',
    title: 'Configure Connection Settings',
    description: 'Enter your system credentials and connection details. All information is encrypted and stored securely. Required fields are marked with a red asterisk.',
    target: '[data-tour="configuration-form"]',
    position: 'top',
    highlight: true
  },
  {
    id: 'connection-test',
    title: 'Test Your Connection',
    description: 'We\'ll verify that your credentials work and can access your data. This ensures everything is set up correctly before we start syncing.',
    target: '[data-tour="connection-test"]',
    position: 'top',
    highlight: true
  },
  {
    id: 'data-preview',
    title: 'Preview Available Data',
    description: 'See a sample of the data we can access from your system. This helps you understand what information will be imported and how it\'s structured.',
    target: '[data-tour="data-preview"]',
    position: 'left',
    highlight: true
  },
  {
    id: 'data-selection',
    title: 'Choose Data to Sync',
    description: 'Select which types of data you want to import and how often. Start with essential data like sales or customer information, then add more as needed.',
    target: '[data-tour="data-selection"]',
    position: 'top',
    highlight: true
  },
  {
    id: 'sync-frequency',
    title: 'Set Sync Frequency',
    description: 'Choose how often data should be updated. More frequent syncing gives you real-time insights but uses more resources. Most businesses start with hourly or daily syncing.',
    target: '[data-tour="sync-frequency"]',
    position: 'right',
    highlight: true
  },
  {
    id: 'sync-settings',
    title: 'Configure Sync Settings',
    description: 'Set batch sizes and error handling preferences. Default settings work for most integrations, but you can adjust them based on your data volume and quality requirements.',
    target: '[data-tour="sync-settings"]',
    position: 'top',
    highlight: true
  },
  {
    id: 'integration-summary',
    title: 'Review Integration Summary',
    description: 'Verify all your settings before activation. You can see connection status, selected data types, sync frequency, and what will happen once the integration is live.',
    target: '[data-tour="integration-summary"]',
    position: 'top',
    highlight: true
  },
  {
    id: 'activation-process',
    title: 'Activate Your Integration',
    description: 'Click "Activate Integration" to start the data sync process. You\'ll receive email confirmation and can monitor progress in the Integrations dashboard.',
    target: '[data-tour="activation-process"]',
    position: 'left',
    highlight: true
  }
];
