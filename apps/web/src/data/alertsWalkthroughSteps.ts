
import { WalkthroughStep } from "@/types/walkthrough";

export const alertsWalkthroughSteps: WalkthroughStep[] = [
  {
    id: "alerts-welcome",
    title: "Welcome to the Alert Center",
    description: "This is your command center for monitoring all system operations. Here you can see real-time alerts about data quality, sync issues, and system health.",
    target: ".alerts-header",
    position: "bottom"
  },
  {
    id: "alerts-filters",
    title: "Filter Your Alerts",
    description: "Use these filters to focus on specific types of alerts. You can filter by severity (Critical, High, Medium, Low), status (Active, Acknowledged, Resolved), and location.",
    target: ".alerts-filters",
    position: "right"
  },
  {
    id: "alerts-severity",
    title: "Understanding Severity Levels",
    description: "Critical alerts require immediate attention (system failures), High alerts need quick action (sync delays), Medium alerts should be monitored (storage warnings), and Low alerts are informational (improvements).",
    target: ".severity-filters",
    position: "right"
  },
  {
    id: "alerts-table",
    title: "Alert Management Table",
    description: "This table shows all your alerts with detailed information. You can select multiple alerts for bulk actions, view details, add comments, or pause specific alerts.",
    target: ".alerts-table",
    position: "top"
  },
  {
    id: "alerts-actions",
    title: "Alert Actions",
    description: "Use these action buttons to view alert details, add comments for team communication, or pause alerts temporarily. You can also perform bulk actions on selected alerts.",
    target: ".alert-actions",
    position: "left"
  },
  {
    id: "alerts-status",
    title: "Alert Status Icons",
    description: "Status icons help you quickly identify alert states: Red X (Active - needs attention), Yellow Clock (Acknowledged - being handled), Green Check (Resolved - completed).",
    target: ".status-icons",
    position: "left"
  },
  {
    id: "alerts-bulk",
    title: "Bulk Operations",
    description: "Select multiple alerts using checkboxes, then use 'Mark All Read' or 'Bulk Actions' to efficiently manage multiple alerts at once. This saves time when dealing with many alerts.",
    target: ".bulk-actions",
    position: "bottom"
  },
  {
    id: "alerts-complete",
    title: "You're All Set!",
    description: "You now know how to monitor and manage your system alerts effectively. Remember to check this page regularly to stay on top of any issues in your data pipeline.",
    target: ".alerts-header",
    position: "bottom"
  }
];
