
import { CardPosition } from '@/types/walkthrough';

export const calculateCardPosition = (element: Element, preferredPosition: string): CardPosition => {
  const rect = element.getBoundingClientRect();
  const cardWidth = 400;
  const cardHeight = 300;
  const margin = 20;

  let top = 0;
  let left = 0;
  let arrow: 'up' | 'down' | 'left' | 'right' = 'down';

  switch (preferredPosition) {
    case 'bottom':
      top = rect.bottom + margin;
      left = Math.max(margin, Math.min(window.innerWidth - cardWidth - margin, rect.left + rect.width / 2 - cardWidth / 2));
      arrow = 'up';
      break;
    case 'top':
      top = rect.top - cardHeight - margin;
      left = Math.max(margin, Math.min(window.innerWidth - cardWidth - margin, rect.left + rect.width / 2 - cardWidth / 2));
      arrow = 'down';
      break;
    case 'right':
      top = Math.max(margin, Math.min(window.innerHeight - cardHeight - margin, rect.top + rect.height / 2 - cardHeight / 2));
      left = rect.right + margin;
      arrow = 'left';
      break;
    case 'left':
      top = Math.max(margin, Math.min(window.innerHeight - cardHeight - margin, rect.top + rect.height / 2 - cardHeight / 2));
      left = rect.left - cardWidth - margin;
      arrow = 'right';
      break;
  }

  // Ensure card stays within viewport
  top = Math.max(margin, Math.min(window.innerHeight - cardHeight - margin, top));
  left = Math.max(margin, Math.min(window.innerWidth - cardWidth - margin, left));

  return { top, left, arrow };
};
