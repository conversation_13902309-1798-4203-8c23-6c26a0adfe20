
import { useState, useEffect } from "react";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AdminSidebar } from "@/components/AdminSidebar";
import { DashboardHeader } from "@/components/DashboardHeader";
import { LocationSelector } from "@/components/LocationSelector";
import { MetricsCards } from "@/components/MetricsCards";
import { ExecutiveMetricsCards } from "@/components/ExecutiveMetricsCards";
import { FinancialPerformanceChart } from "@/components/FinancialPerformanceChart";
import { OperationalEfficiencyTable } from "@/components/OperationalEfficiencyTable";
import { BusinessAlertsPanel } from "@/components/BusinessAlertsPanel";
import { OnboardingWelcomeCard } from "@/components/OnboardingWelcomeCard";
import { VisualWalkthrough } from "@/components/VisualWalkthrough";
import { WalkthroughTrigger } from "@/components/WalkthroughTrigger";
import { useWalkthrough } from "@/hooks/useWalkthrough";
import { walkthroughSteps } from "@/data/walkthroughSteps";

const Index = () => {
  const [selectedLocationId, setSelectedLocationId] = useState<string | null>("all");
  const [showWelcomeCard, setShowWelcomeCard] = useState(false);
  
  const {
    showWalkthrough,
    hasSeenWalkthrough,
    completeWalkthrough,
    skipWalkthrough,
    startWalkthrough
  } = useWalkthrough();

  useEffect(() => {
    const welcomeCardDismissed = localStorage.getItem('welcome-card-dismissed');
    const walkthroughCompleted = localStorage.getItem('walkthrough-completed');
    
    if (!welcomeCardDismissed && !walkthroughCompleted) {
      setShowWelcomeCard(true);
    }
  }, []);

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-gradient-to-br from-orange-50 via-white to-orange-100">
        <AdminSidebar />
        <main className="flex-1 flex flex-col">
          <DashboardHeader />
          <div className="flex-1 p-6 space-y-6">
            {showWelcomeCard && (
              <OnboardingWelcomeCard />
            )}
            
            <div className="executive-header">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Executive Dashboard</h1>
              <p className="text-gray-600 platform-summary">
                Real-time business intelligence and analytics for strategic decision-making
              </p>
            </div>

            <div className="location-selector">
              <LocationSelector 
                selectedLocationId={selectedLocationId}
                onLocationChange={setSelectedLocationId}
              />
            </div>

            <div className="metrics-cards">
              {selectedLocationId === "all" ? (
                <ExecutiveMetricsCards />
              ) : (
                <MetricsCards />
              )}
            </div>

            <div className="grid lg:grid-cols-3 gap-6">
              <div className="lg:col-span-3 space-y-6">
                <div className="financial-performance">
                  <FinancialPerformanceChart locationId={selectedLocationId === "all" ? null : selectedLocationId} />
                </div>
                
                <div className="grid lg:grid-cols-3 gap-6">
                  <div className="lg:col-span-2">
                    <div className="operational-efficiency">
                      <OperationalEfficiencyTable />
                    </div>
                  </div>

                  <div className="business-alerts">
                    <BusinessAlertsPanel />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>

        {showWalkthrough && (
          <VisualWalkthrough
            steps={walkthroughSteps}
            onComplete={completeWalkthrough}
            onSkip={skipWalkthrough}
          />
        )}
        
        {hasSeenWalkthrough && (
          <WalkthroughTrigger onStart={startWalkthrough} />
        )}
      </div>
    </SidebarProvider>
  );
};

export default Index;
