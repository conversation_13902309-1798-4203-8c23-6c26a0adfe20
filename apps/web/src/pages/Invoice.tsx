
import React from 'react';
import { InvoiceTemplate } from '@/components/invoice/InvoiceTemplate';
import { sampleInvoice } from '@/data/sampleInvoice';
import { useToast } from '@/hooks/use-toast';

export default function Invoice() {
  const { toast } = useToast();

  const handleDownload = () => {
    toast({
      title: "Download Started",
      description: "Your invoice PDF is being generated...",
    });
    // In a real app, this would trigger PDF generation
    window.print();
  };

  const handleEmail = () => {
    toast({
      title: "Email Sent",
      description: "Invoice has been sent to the client's email address.",
    });
    // In a real app, this would send the invoice via email
  };

  const handlePrint = () => {
    window.print();
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <InvoiceTemplate
          invoice={sampleInvoice}
          onDownload={handleDownload}
          onEmail={handleEmail}
          onPrint={handlePrint}
        />
      </div>
    </div>
  );
}
