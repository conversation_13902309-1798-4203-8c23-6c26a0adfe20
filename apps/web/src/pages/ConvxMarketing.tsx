
import { AdminSidebar } from "@/components/AdminSidebar";
import { DashboardHeader } from "@/components/DashboardHeader";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ConvxMarketingLogo } from "@/components/ConvxMarketingLogo";

export default function ConvxMarketing() {
  return (
    <div className="min-h-screen bg-gray-50 flex">
      <AdminSidebar />
      <div className="flex-1 flex flex-col">
        <DashboardHeader />
        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            <div className="flex items-center gap-3 mb-6">
              <ConvxMarketingLogo width={48} height={48} />
              <div>
                <h1 className="text-3xl font-bold text-gray-900">CONVX Marketing</h1>
                <p className="text-gray-600 mt-1">Marketing analytics and campaign management</p>
              </div>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Marketing Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  This section will contain marketing analytics, campaign management, and customer insights.
                  More functionality will be added soon.
                </p>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
