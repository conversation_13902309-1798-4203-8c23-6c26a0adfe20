
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useOnboarding } from "@/contexts/OnboardingContext";
import { useNavigate } from "react-router-dom";

export default function WelcomeExistingUser() {
  const { company, setCurrentStep } = useOnboarding();
  const navigate = useNavigate();

  const handleJoinNow = () => {
    setCurrentStep(2);
    navigate('/onboarding/profile-setup');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-100">
      <Card className="w-full max-w-md mx-4">
        <CardHeader className="text-center pb-4">
          <div className="w-16 h-16 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-4">
            <span className="text-white font-bold text-2xl">C</span>
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">Welcome to CONVX</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-6">
          <p className="text-gray-600 text-lg">
            You've been invited to join <strong>{company?.name || 'your team'}</strong>.
          </p>
          <Button 
            onClick={handleJoinNow}
            className="w-full py-3 text-lg bg-green-600 hover:bg-green-700"
            size="lg"
          >
            Join Now
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
