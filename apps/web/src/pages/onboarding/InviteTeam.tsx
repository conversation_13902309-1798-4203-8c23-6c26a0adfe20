
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { useOnboarding } from "@/contexts/OnboardingContext";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useTeamInvitations } from "@/hooks/useTeamInvitations";
import { TeamMember } from "@/types/teamMember";
import { TeamMemberForm } from "@/components/onboarding/TeamMemberForm";
import { TeamMembersList } from "@/components/onboarding/TeamMembersList";
import { InviteTeamActions } from "@/components/onboarding/InviteTeamActions";

export default function InviteTeam() {
  const { setCurrentStep } = useOnboarding();
  const { sendInvitation } = useTeamInvitations();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(false);

  const addTeamMember = (member: TeamMember) => {
    if (!member.email.trim()) {
      toast({
        title: "Missing email",
        description: "Please enter an email address.",
        variant: "destructive"
      });
      return;
    }

    if (teamMembers.some(existingMember => existingMember.email === member.email)) {
      toast({
        title: "Duplicate email",
        description: "This email has already been added.",
        variant: "destructive"
      });
      return;
    }

    setTeamMembers([...teamMembers, member]);
  };

  const removeTeamMember = (index: number) => {
    setTeamMembers(teamMembers.filter((_, i) => i !== index));
  };

  const handleSendInvitations = async () => {
    if (teamMembers.length === 0) {
      // Skip team invitations
      setCurrentStep(5);
      navigate('/onboarding/profile-setup');
      return;
    }

    setLoading(true);
    try {
      // Send all invitations
      await Promise.all(
        teamMembers.map(member => sendInvitation(member.email, member.role))
      );

      toast({
        title: "Invitations sent",
        description: `${teamMembers.length} invitation(s) sent successfully!`,
      });

      setCurrentStep(5);
      navigate('/onboarding/profile-setup');
    } catch (error) {
      console.error('Error sending invitations:', error);
      toast({
        title: "Error",
        description: "Failed to send some invitations. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSkip = () => {
    setCurrentStep(5);
    navigate('/onboarding/profile-setup');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <Card className="w-full max-w-2xl mx-4">
        <CardHeader className="text-center pb-4">
          <CardTitle className="text-2xl font-bold text-gray-900">Invite Your Team</CardTitle>
          <p className="text-gray-600">Add team members to get started collaborating</p>
        </CardHeader>
        <CardContent className="space-y-6">
          <TeamMemberForm onAddMember={addTeamMember} loading={loading} />
          
          <TeamMembersList 
            teamMembers={teamMembers} 
            onRemoveMember={removeTeamMember} 
            loading={loading} 
          />

          <InviteTeamActions
            onSkip={handleSkip}
            onSendInvitations={handleSendInvitations}
            loading={loading}
            teamMembersCount={teamMembers.length}
          />
        </CardContent>
      </Card>
    </div>
  );
}
