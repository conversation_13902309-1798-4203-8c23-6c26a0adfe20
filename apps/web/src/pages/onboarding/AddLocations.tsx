
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { useOnboarding } from "@/contexts/OnboardingContext";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useRestaurantLocations, RestaurantLocationForm } from "@/hooks/useRestaurantLocations";
import { X, Plus, Loader2, MapPin, TrendingUp, CloudSun } from "lucide-react";

export default function AddLocations() {
  const { setCurrentStep } = useOnboarding();
  const { createMultipleLocations, isCreating } = useRestaurantLocations();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [locations, setLocations] = useState<RestaurantLocationForm[]>([]);
  const [currentLocation, setCurrentLocation] = useState<RestaurantLocationForm>({
    name: '',
    address: '',
    city: '',
    state: '',
    zip_code: '',
    phone: '',
    manager_name: ''
  });

  const addLocation = () => {
    if (!currentLocation.name.trim()) {
      toast({
        title: "Missing location name",
        description: "Please enter a location name.",
        variant: "destructive"
      });
      return;
    }

    if (locations.some(loc => loc.name === currentLocation.name)) {
      toast({
        title: "Duplicate location",
        description: "A location with this name already exists.",
        variant: "destructive"
      });
      return;
    }

    setLocations([...locations, currentLocation]);
    setCurrentLocation({
      name: '',
      address: '',
      city: '',
      state: '',
      zip_code: '',
      phone: '',
      manager_name: ''
    });
  };

  const removeLocation = (index: number) => {
    setLocations(locations.filter((_, i) => i !== index));
  };

  const handleContinue = async () => {
    if (locations.length === 0) {
      // Skip locations
      setCurrentStep(4);
      navigate('/onboarding/invite-team');
      return;
    }

    try {
      await createMultipleLocations(locations);

      toast({
        title: "Locations added successfully",
        description: `${locations.length} location(s) added. We'll start gathering insights immediately!`,
      });

      setCurrentStep(4);
      navigate('/onboarding/invite-team');
    } catch (error) {
      console.error('Error creating locations:', error);
      toast({
        title: "Error",
        description: "Failed to create some locations. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleSkip = () => {
    setCurrentStep(4);
    navigate('/onboarding/invite-team');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <Card className="w-full max-w-4xl mx-4">
        <CardHeader className="text-center pb-4">
          <CardTitle className="text-2xl font-bold text-gray-900">Add Your Restaurant Locations</CardTitle>
          <p className="text-gray-600">Add locations to start gathering valuable insights immediately</p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Value Proposition */}
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <h3 className="font-semibold text-orange-800 mb-3">What you'll get immediately:</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-orange-600" />
                <span className="text-sm text-orange-700">Public review analytics</span>
              </div>
              <div className="flex items-center space-x-2">
                <CloudSun className="w-5 h-5 text-orange-600" />
                <span className="text-sm text-orange-700">Weather impact insights</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="w-5 h-5 text-orange-600" />
                <span className="text-sm text-orange-700">Location performance data</span>
              </div>
            </div>
          </div>

          {/* Add Location Form */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Location Name *</Label>
                <Input
                  id="name"
                  placeholder="Downtown Location"
                  value={currentLocation.name}
                  onChange={(e) => setCurrentLocation({ ...currentLocation, name: e.target.value })}
                  disabled={isCreating}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="address">Address</Label>
                <Input
                  id="address"
                  placeholder="123 Main Street"
                  value={currentLocation.address}
                  onChange={(e) => setCurrentLocation({ ...currentLocation, address: e.target.value })}
                  disabled={isCreating}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  placeholder="New York"
                  value={currentLocation.city}
                  onChange={(e) => setCurrentLocation({ ...currentLocation, city: e.target.value })}
                  disabled={isCreating}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  placeholder="NY"
                  value={currentLocation.state}
                  onChange={(e) => setCurrentLocation({ ...currentLocation, state: e.target.value })}
                  disabled={isCreating}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="zip">ZIP Code</Label>
                <Input
                  id="zip"
                  placeholder="10001"
                  value={currentLocation.zip_code}
                  onChange={(e) => setCurrentLocation({ ...currentLocation, zip_code: e.target.value })}
                  disabled={isCreating}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  placeholder="(*************"
                  value={currentLocation.phone}
                  onChange={(e) => setCurrentLocation({ ...currentLocation, phone: e.target.value })}
                  disabled={isCreating}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="manager">Manager Name</Label>
                <Input
                  id="manager"
                  placeholder="John Smith"
                  value={currentLocation.manager_name}
                  onChange={(e) => setCurrentLocation({ ...currentLocation, manager_name: e.target.value })}
                  disabled={isCreating}
                />
              </div>
            </div>
            
            <Button 
              type="button" 
              variant="outline" 
              onClick={addLocation}
              className="w-full"
              disabled={isCreating}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Location
            </Button>
          </div>

          {/* Added Locations */}
          {locations.length > 0 && (
            <div className="space-y-3">
              <Label>Restaurant Locations ({locations.length})</Label>
              <div className="space-y-2">
                {locations.map((location, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <MapPin className="w-4 h-4 text-gray-500" />
                      <div>
                        <span className="font-medium">{location.name}</span>
                        {(location.city || location.state) && (
                          <span className="text-sm text-gray-500 ml-2">
                            {[location.city, location.state].filter(Boolean).join(', ')}
                          </span>
                        )}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeLocation(index)}
                      disabled={isCreating}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="flex space-x-3 pt-4">
            <Button 
              variant="outline" 
              onClick={handleSkip}
              className="flex-1"
              disabled={isCreating}
            >
              Skip for Now
            </Button>
            <Button 
              onClick={handleContinue}
              className="flex-1"
              disabled={isCreating}
            >
              {isCreating ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Adding Locations...
                </>
              ) : locations.length > 0 ? (
                `Continue with ${locations.length} Location${locations.length > 1 ? 's' : ''}`
              ) : (
                'Continue'
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
