
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { useOnboarding } from "@/contexts/OnboardingContext";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useCompany } from "@/hooks/useCompany";
import { CompanyForm, CompanyFormData } from "@/components/onboarding/CompanyForm";
import { CompanyFormActions } from "@/components/onboarding/CompanyFormActions";

export default function CreateCompany() {
  const { setCurrentStep } = useOnboarding();
  const { createCompany } = useCompany();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState<CompanyFormData>({
    name: '',
    industry: ''
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.industry) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      console.log('Submitting company creation form');
      
      await createCompany({
        name: formData.name,
        industry: formData.industry
      });
      
      console.log('Company created successfully, proceeding to add locations');
      
      toast({
        title: "Success",
        description: "Company created successfully!",
      });
      
      setCurrentStep(3);
      navigate('/onboarding/add-locations');
    } catch (error) {
      console.error('Error creating company:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Failed to create company. Please try again.';
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <Card className="w-full max-w-md mx-4">
        <CardHeader className="text-center pb-4">
          <CardTitle className="text-2xl font-bold text-gray-900">Create Your Company</CardTitle>
          <p className="text-gray-600">Set up your organization profile</p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <CompanyForm 
              formData={formData}
              onFormDataChange={setFormData}
              loading={loading}
            />
            
            <CompanyFormActions 
              formData={formData}
              loading={loading}
            />
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
