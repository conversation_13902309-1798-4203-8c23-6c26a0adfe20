
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { X, Zap } from "lucide-react";
import { useNavigate } from "react-router-dom";

export function OnboardingWelcomeCard() {
  const [isVisible, setIsVisible] = useState(true);
  const navigate = useNavigate();

  if (!isVisible) return null;

  const handleSetupIntegration = () => {
    navigate('/integrations');
    setIsVisible(false);
  };

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('welcome-card-dismissed', 'true');
  };

  return (
    <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 mb-6">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-sm">👋</span>
            </div>
            <CardTitle className="text-lg font-semibold text-gray-900">
              Welcome to CONVX
            </CardTitle>
          </div>
          <Button variant="ghost" size="sm" onClick={handleDismiss}>
            <X className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-gray-600 mb-4">
          Start by setting up your first integration to begin monitoring your restaurant data pipeline.
        </p>
        <Button onClick={handleSetupIntegration} className="bg-blue-600 hover:bg-blue-700">
          <Zap className="w-4 h-4 mr-2" />
          Set Up Integration
        </Button>
      </CardContent>
    </Card>
  );
}
