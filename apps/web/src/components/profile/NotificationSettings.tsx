
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Bell } from "lucide-react";

interface NotificationSettingsProps {
  emailAlerts: boolean;
  smsAlerts: boolean;
  isEditing: boolean;
  onEmailAlertsChange: (checked: boolean) => void;
  onSmsAlertsChange: (checked: boolean) => void;
}

export function NotificationSettings({
  emailAlerts,
  smsAlerts,
  isEditing,
  onEmailAlertsChange,
  onSmsAlertsChange
}: NotificationSettingsProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Bell className="w-5 h-5" />
        <h3 className="text-lg font-semibold">Notification Preferences</h3>
      </div>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="emailAlerts">Email Notifications</Label>
            <p className="text-sm text-gray-500">
              Receive email notifications for alerts and updates
            </p>
          </div>
          <Switch
            id="emailAlerts"
            checked={emailAlerts}
            onCheckedChange={onEmailAlertsChange}
            disabled={!isEditing}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="smsAlerts">SMS Notifications</Label>
            <p className="text-sm text-gray-500">
              Receive text messages for critical alerts
            </p>
          </div>
          <Switch
            id="smsAlerts"
            checked={smsAlerts}
            onCheckedChange={onSmsAlertsChange}
            disabled={!isEditing}
          />
        </div>
      </div>
    </div>
  );
}
