
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { User, Save } from "lucide-react";
import { NotificationSettings } from "./NotificationSettings";

interface FormData {
  fullName: string;
  email: string;
  role: "CEO" | "COO" | "Data Admin" | "Business Admin";
  smsAlerts: boolean;
  emailAlerts: boolean;
  phone: string;
  timezone: string;
}

interface ProfileDetailsCardProps {
  formData: FormData;
  isEditing: boolean;
  onFormDataChange: (data: FormData) => void;
  onEditToggle: () => void;
  onSave: () => void;
}

export function ProfileDetailsCard({
  formData,
  isEditing,
  onFormDataChange,
  onEditToggle,
  onSave
}: ProfileDetailsCardProps) {
  const handleFieldChange = (field: keyof FormData, value: any) => {
    onFormDataChange({ ...formData, [field]: value });
  };

  return (
    <Card className="lg:col-span-2">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="flex items-center space-x-2">
            <User className="w-5 h-5" />
            <span>Profile Information</span>
          </CardTitle>
        </div>
        <Button
          variant={isEditing ? "outline" : "default"}
          onClick={onEditToggle}
        >
          {isEditing ? "Cancel" : "Edit"}
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="fullName">Full Name</Label>
            <Input
              id="fullName"
              value={formData.fullName}
              onChange={(e) => handleFieldChange('fullName', e.target.value)}
              disabled={!isEditing}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              value={formData.email}
              disabled={true}
              className="bg-gray-50"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              value={formData.phone}
              onChange={(e) => handleFieldChange('phone', e.target.value)}
              disabled={!isEditing}
              placeholder="(*************"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="role">Role</Label>
            <Select
              value={formData.role}
              onValueChange={(value: "CEO" | "COO" | "Data Admin" | "Business Admin") => handleFieldChange('role', value)}
              disabled={!isEditing}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="CEO">CEO</SelectItem>
                <SelectItem value="COO">COO</SelectItem>
                <SelectItem value="Data Admin">Data Admin</SelectItem>
                <SelectItem value="Business Admin">Business Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="timezone">Timezone</Label>
            <Select
              value={formData.timezone}
              onValueChange={(value) => handleFieldChange('timezone', value)}
              disabled={!isEditing}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="America/New_York">Eastern Time (ET)</SelectItem>
                <SelectItem value="America/Chicago">Central Time (CT)</SelectItem>
                <SelectItem value="America/Denver">Mountain Time (MT)</SelectItem>
                <SelectItem value="America/Los_Angeles">Pacific Time (PT)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Separator />

        <NotificationSettings
          emailAlerts={formData.emailAlerts}
          smsAlerts={formData.smsAlerts}
          isEditing={isEditing}
          onEmailAlertsChange={(checked) => handleFieldChange('emailAlerts', checked)}
          onSmsAlertsChange={(checked) => handleFieldChange('smsAlerts', checked)}
        />

        {isEditing && (
          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={onEditToggle}>
              Cancel
            </Button>
            <Button onClick={onSave} className="bg-green-600 hover:bg-green-700">
              <Save className="w-4 h-4 mr-2" />
              Save Changes
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
