
import { But<PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { CompanyFormData } from "./CompanyForm";

interface CompanyFormActionsProps {
  formData: CompanyFormData;
  loading: boolean;
}

export function CompanyFormActions({ formData, loading }: CompanyFormActionsProps) {
  return (
    <Button 
      type="submit" 
      className="w-full mt-6" 
      disabled={loading || !formData.name || !formData.industry}
    >
      {loading ? (
        <>
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          Creating Company...
        </>
      ) : (
        'Create Company'
      )}
    </Button>
  );
}
