
import { Target } from "lucide-react";
import { DataTypeDetails } from "./DataTypeInfo";

interface BestPracticesSectionProps {
  typeInfo: DataTypeDetails;
}

export function BestPracticesSection({ typeInfo }: BestPracticesSectionProps) {
  return (
    <div className="bg-white rounded-lg p-4 border border-blue-200">
      <h4 className="font-semibold text-blue-800 mb-3 flex items-center gap-2">
        <Target className="w-4 h-4" />
        Best Practices
      </h4>
      <div className="space-y-2">
        {typeInfo.tips.map((tip, index) => (
          <div key={index} className="flex items-start gap-2 text-sm text-blue-700">
            <span className="text-orange-500 font-bold mt-0.5 flex-shrink-0">•</span>
            {tip}
          </div>
        ))}
      </div>
    </div>
  );
}
