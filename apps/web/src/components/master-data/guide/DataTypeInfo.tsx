
export interface DataTypeExample {
  good: string[];
  mapped: string;
  explanation: string;
}

export interface DataTypeDetails {
  title: string;
  description: string;
  icon: string;
  examples: DataTypeExample;
  benefits: string[];
  tips: string[];
}

export const dataTypeInfo: Record<string, DataTypeDetails> = {
  "menu-items": {
    title: "Menu Items",
    description: "Standardize your menu item names across all systems",
    icon: "🍽️",
    examples: {
      good: ["Caesar Salad", "Chicken Caesar Salad", "Caesar Salad with Chicken"],
      mapped: "Caesar Salad with Chicken (Optional)",
      explanation: "These should all match to one master item since they're the same dish"
    },
    benefits: [
      "Track true item performance across locations",
      "Identify your best-selling dishes accurately", 
      "Optimize menu based on real data",
      "Standardize pricing across locations"
    ],
    tips: [
      "Group items that are essentially the same dish",
      "Consider size variations as the same item",
      "Match seasonal variations to the base item",
      "Use the most descriptive name as your master"
    ]
  },
  "menu-categories": {
    title: "Menu Categories", 
    description: "Organize your menu structure consistently",
    icon: "📋",
    examples: {
      good: ["Appetizers", "Starters", "Apps", "Small Plates"],
      mapped: "Appetizers",
      explanation: "These are all the same category type and should be unified"
    },
    benefits: [
      "Consistent menu organization",
      "Better customer navigation",
      "Category performance insights",
      "Streamlined menu management"
    ],
    tips: [
      "Use clear, customer-friendly category names",
      "Consider grouping similar categories",
      "Think about your customer's ordering journey",
      "Keep categories broad but meaningful"
    ]
  },
  "employees": {
    title: "Employees",
    description: "Connect employee records across all systems", 
    icon: "👨‍💼",
    examples: {
      good: ["John Smith", "J. Smith", "John S.", "Smith, John"],
      mapped: "John Smith",
      explanation: "These all refer to the same employee across different systems"
    },
    benefits: [
      "Unified employee performance tracking",
      "Accurate scheduling across systems",
      "Complete payroll integration",
      "Better staff management insights"
    ],
    tips: [
      "Use full legal names when possible",
      "Consider nickname variations",
      "Check employee ID numbers for verification",
      "Match based on unique identifiers when available"
    ]
  },
  "ingredients-supplies": {
    title: "Ingredients & Supplies",
    description: "Standardize inventory item names",
    icon: "📦", 
    examples: {
      good: ["Roma Tomatoes", "Roma Tomato", "Tomatoes - Roma", "Roma Tomatoes (lb)"],
      mapped: "Roma Tomatoes",
      explanation: "Same ingredient with different naming conventions"
    },
    benefits: [
      "Accurate inventory tracking",
      "Better cost management", 
      "Optimized purchasing decisions",
      "Waste reduction insights"
    ],
    tips: [
      "Focus on the base ingredient name",
      "Ignore unit differences when matching",
      "Consider brand variations of same item",
      "Group similar quality grades together"
    ]
  },
  "locations": {
    title: "Locations",
    description: "Standardize location names across systems",
    icon: "📍",
    examples: {
      good: ["NYC Times Square", "New York - Times Square", "Times Square Location"],
      mapped: "New York - Times Square",
      explanation: "These all refer to the same physical location"
    },
    benefits: [
      "Accurate location-based reporting",
      "Consistent performance tracking",
      "Better operational oversight",
      "Streamlined multi-location management"
    ],
    tips: [
      "Use clear, descriptive location names",
      "Include city and landmark when helpful",
      "Consider abbreviations and full names",
      "Match based on address when uncertain"
    ]
  }
};

export const getDataTypeInfo = (dataType: string): DataTypeDetails => {
  return dataTypeInfo[dataType] || dataTypeInfo["menu-items"];
};
