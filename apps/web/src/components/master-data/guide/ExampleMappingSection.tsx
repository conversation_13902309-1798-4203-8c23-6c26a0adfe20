
import { Badge } from "@/components/ui/badge";
import { Lightbulb } from "lucide-react";
import { DataTypeDetails } from "./DataTypeInfo";

interface ExampleMappingSectionProps {
  typeInfo: DataTypeDetails;
}

export function ExampleMappingSection({ typeInfo }: ExampleMappingSectionProps) {
  return (
    <div className="bg-white rounded-lg p-4 border border-blue-200">
      <h4 className="font-semibold text-blue-800 mb-3 flex items-center gap-2">
        <Lightbulb className="w-4 h-4" />
        Example Mapping
      </h4>
      <div className="space-y-2">
        <div className="text-sm">
          <span className="font-medium text-gray-700">These items:</span>
          <div className="flex flex-wrap gap-1 mt-1">
            {typeInfo.examples.good.map((item, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {item}
              </Badge>
            ))}
          </div>
        </div>
        <div className="flex items-center gap-2 text-sm">
          <span className="font-medium text-gray-700">Should map to:</span>
          <Badge className="bg-green-100 text-green-800">
            {typeInfo.examples.mapped}
          </Badge>
        </div>
        <p className="text-xs text-gray-600 italic">
          {typeInfo.examples.explanation}
        </p>
      </div>
    </div>
  );
}
