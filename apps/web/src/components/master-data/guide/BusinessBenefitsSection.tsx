
import { CheckCircle, TrendingUp } from "lucide-react";
import { DataTypeDetails } from "./DataTypeInfo";

interface BusinessBenefitsSectionProps {
  typeInfo: DataTypeDetails;
}

export function BusinessBenefitsSection({ typeInfo }: BusinessBenefitsSectionProps) {
  return (
    <div className="bg-white rounded-lg p-4 border border-blue-200">
      <h4 className="font-semibold text-blue-800 mb-3 flex items-center gap-2">
        <TrendingUp className="w-4 h-4" />
        Business Benefits
      </h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
        {typeInfo.benefits.map((benefit, index) => (
          <div key={index} className="flex items-start gap-2 text-sm text-blue-700">
            <CheckCircle className="w-4 h-4 mt-0.5 text-green-600 flex-shrink-0" />
            {benefit}
          </div>
        ))}
      </div>
    </div>
  );
}
