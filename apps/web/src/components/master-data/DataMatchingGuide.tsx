
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Target, Lightbulb, TrendingUp, CheckCircle, Info } from "lucide-react";
import { ExampleMatchingSection } from "./guide/ExampleMatchingSection";
import { BusinessBenefitsSection } from "./guide/BusinessBenefitsSection";
import { BestPracticesSection } from "./guide/BestPracticesSection";
import { getDataTypeInfo } from "./guide/DataTypeInfo";

interface DataMatchingGuideProps {
  dataType: string;
}

export function DataMatchingGuide({ dataType }: DataMatchingGuideProps) {
  const typeInfo = getDataTypeInfo(dataType);
  
  if (!typeInfo) return null;

  return (
    <div className="grid md:grid-cols-2 gap-4 mb-6">
      {/* Quick Guide */}
      <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <Target className="w-5 h-5" />
            Quick Matching Guide
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold text-blue-800 mb-2">What You're Matching</h4>
            <p className="text-sm text-blue-700">{typeInfo.description}</p>
          </div>
          
          <ExampleMatchingSection typeInfo={typeInfo} />
          
          <Alert className="border-blue-200 bg-blue-50">
            <Info className="w-4 h-4 text-blue-600" />
            <AlertDescription className="text-sm text-blue-800">
              <strong>Goal:</strong> Create consistent data standards across all your systems
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Business Benefits */}
      <div className="space-y-4">
        <BusinessBenefitsSection typeInfo={typeInfo} />
        <BestPracticesSection typeInfo={typeInfo} />
      </div>
    </div>
  );
}
