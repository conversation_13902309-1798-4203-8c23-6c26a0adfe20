
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info } from "lucide-react";

interface MappingExampleProps {
  type: string;
}

const getBusinessValueInfo = (type: string) => {
  const info = {
    "Menu Item": {
      example: "Map 'Chicken Caesar Salad' and 'Caesar w/ Chicken' to the same master item for unified reporting."
    },
    "Menu Category": {
      example: "Map 'Appetizers', 'Starters', and 'Apps' to the same 'Appetizers' category."
    },
    "Survey Question": {
      example: "Map 'How was your service?' and 'Rate our service' to the same question for unified analysis."
    },
    "Employee": {
      example: "Map '<PERSON>' and '<PERSON><PERSON>' to the same employee record."
    },
    "Ingredient": {
      example: "Map 'Roma Tomatoes' and 'Roma Tomato' to the same ingredient."
    }
  };
  
  return info[type] || info["Menu Item"];
};

export function MappingExample({ type }: MappingExampleProps) {
  const businessInfo = getBusinessValueInfo(type);

  return (
    <Alert className="border-orange-200 bg-orange-50">
      <Info className="w-4 h-4 text-orange-600" />
      <AlertDescription className="text-sm text-orange-800">
        <strong>Example:</strong> {businessInfo.example}
      </AlertDescription>
    </Alert>
  );
}
