
import { TrendingUp, Check } from "lucide-react";

interface BusinessValueCardProps {
  type: string;
}

const getBusinessValueInfo = (type: string) => {
  const info = {
    "Menu Item": {
      title: "Standardize Your Menu Items",
      description: "Group similar dishes together to get accurate sales reporting and identify your best performers.",
      benefits: [
        "📊 Clear sales analytics across all locations",
        "🎯 Identify top-performing items",
        "📈 Track menu performance trends",
        "💰 Optimize pricing strategies"
      ],
      example: "Map 'Chicken Caesar Salad' and 'Caesar w/ Chicken' to the same master item for unified reporting."
    },
    "Menu Category": {
      title: "Organize Your Menu Structure",
      description: "Create consistent menu categories to better understand customer preferences and optimize your offerings.",
      benefits: [
        "📋 Unified menu organization",
        "🍽️ Better customer experience",
        "📊 Category performance insights",
        "🎯 Targeted promotions"
      ],
      example: "Map 'Appetizers', 'Starters', and 'Apps' to the same 'Appetizers' category."
    },
    "Survey Question": {
      title: "Standardize Customer Feedback",
      description: "Unify similar survey questions to get comprehensive insights from all your feedback channels.",
      benefits: [
        "📊 Complete customer satisfaction view",
        "🎯 Identify improvement areas",
        "📈 Track satisfaction trends",
        "💡 Actionable insights"
      ],
      example: "Map 'How was your service?' and 'Rate our service' to the same question for unified analysis."
    },
    "Employee": {
      title: "Connect Employee Data",
      description: "Link employee records across systems to get complete performance and scheduling insights.",
      benefits: [
        "👥 Unified employee profiles",
        "📊 Complete performance tracking",
        "⏰ Better scheduling insights",
        "💰 Accurate payroll data"
      ],
      example: "Map 'John Smith' and 'J. Smith' to the same employee record."
    },
    "Ingredient": {
      title: "Standardize Inventory Items",
      description: "Unify ingredient and supply names to optimize inventory management and cost control.",
      benefits: [
        "📦 Accurate inventory tracking",
        "💰 Better cost management",
        "🚚 Optimized ordering",
        "📊 Waste reduction insights"
      ],
      example: "Map 'Roma Tomatoes' and 'Roma Tomato' to the same ingredient."
    }
  };
  
  return info[type] || info["Menu Item"];
};

export function BusinessValueCard({ type }: BusinessValueCardProps) {
  const businessInfo = getBusinessValueInfo(type);

  return (
    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border-2 border-blue-200">
      <div className="flex items-center gap-2 mb-3">
        <TrendingUp className="w-5 h-5 text-blue-600" />
        <h3 className="font-semibold text-blue-800">Why This Matters</h3>
      </div>
      <p className="text-sm text-blue-700 mb-3">{businessInfo.description}</p>
      <div className="space-y-2">
        {businessInfo.benefits.map((benefit, index) => (
          <div key={index} className="text-xs text-blue-600 flex items-center gap-1">
            <Check className="w-3 h-3" />
            {benefit}
          </div>
        ))}
      </div>
    </div>
  );
}
