
import { Check } from "lucide-react";

interface MappingOutcomeProps {
  type: string;
}

export function MappingOutcome({ type }: MappingOutcomeProps) {
  return (
    <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200">
      <h4 className="font-medium text-green-800 mb-2 flex items-center gap-2">
        <Check className="w-4 h-4" />
        What Happens Next
      </h4>
      <div className="text-sm text-green-700 space-y-1">
        <div>✅ Data will be unified under the selected {type.toLowerCase()}</div>
        <div>📊 Reports will show consolidated analytics</div>
        <div>🎯 Future data will auto-map to this choice</div>
        <div>📈 You'll see improved data quality scores</div>
      </div>
    </div>
  );
}
