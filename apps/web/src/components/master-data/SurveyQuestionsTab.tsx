
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Target, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { MatchingModal } from "./MatchingModal";
import { DataMatchingGuide } from "./DataMatchingGuide";

const unmatchedSurveyQuestions = [
  { id: 1, sourceValue: "How satisfied are you with service?", frequency: 145, source: "Survey Platform", lastSeen: "2024-06-03" },
  { id: 2, sourceValue: "Rate food quality 1-10", frequency: 123, source: "Feedback System", lastSeen: "2024-06-03" },
  { id: 3, sourceValue: "Would you recommend us?", frequency: 98, source: "Review Platform", lastSeen: "2024-06-02" },
  { id: 4, sourceValue: "Wait time satisfaction", frequency: 87, source: "Custom Survey", lastSeen: "2024-06-03" },
];

const masterSurveyQuestions = [
  "Service Satisfaction (1-5 Scale)",
  "Food Quality Rating (1-10 Scale)",
  "Net Promoter Score (0-10)",
  "Wait Time Satisfaction (Yes/No)",
  "Overall Experience Rating",
  "Value for Money Rating",
];

export function SurveyQuestionsTab() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedItem, setSelectedItem] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const filteredItems = unmatchedSurveyQuestions.filter(item =>
    item.sourceValue.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleMatchItem = (item) => {
    setSelectedItem(item);
    setIsModalOpen(true);
  };

  const handleSaveMatching = (matching) => {
    console.log("Saving survey questions matching:", matching);
    setIsModalOpen(false);
    setSelectedItem(null);
  };

  return (
    <div className="space-y-6">
      {/* Help Guide */}
      <DataMatchingGuide dataType="survey-questions" />
      
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search unmatched survey questions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-80"
            />
          </div>
          <Badge variant="secondary" className="bg-orange-100 text-orange-800">
            {filteredItems.length} unmatched questions
          </Badge>
        </div>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Source Value</TableHead>
              <TableHead>Source System</TableHead>
              <TableHead>Frequency</TableHead>
              <TableHead>Last Seen</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredItems.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.sourceValue}</TableCell>
                <TableCell>
                  <Badge variant="outline">{item.source}</Badge>
                </TableCell>
                <TableCell>{item.frequency}</TableCell>
                <TableCell>{item.lastSeen}</TableCell>
                <TableCell>
                  <Button
                    size="sm"
                    onClick={() => handleMatchItem(item)}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Target className="w-4 h-4 mr-1" />
                    Match
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <MatchingModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        item={selectedItem}
        masterOptions={masterSurveyQuestions}
        onSave={handleSaveMatching}
        type="Survey Question"
      />
    </div>
  );
}
