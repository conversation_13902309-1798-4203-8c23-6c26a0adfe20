
import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON>nt,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Edit, ChefHat, Sparkles } from "lucide-react";

interface CategoryItemModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemName: string;
  onSave: (categoryData: any) => void;
}

export function CategoryItemModal({ isOpen, onClose, itemName, onSave }: CategoryItemModalProps) {
  const [formData, setFormData] = useState({
    itemName: itemName,
    itemSKU: "12345678",
    category: "",
    subCategory: "",
    itemPLU: "12345678",
    receivedItemName: itemName, // This will be read-only and show the source value
    tags: ""
  });

  const categories = [
    "Appetizer",
    "Entree", 
    "Dessert",
    "Beverage",
    "Side Dish"
  ];

  const subCategories = {
    "Appetizer": ["Hot Appetizer", "Cold Appetizer", "Finger Food"],
    "Entree": ["Seafood", "Meat", "Vegetarian", "Pasta"],
    "Dessert": ["Ice Cream", "Cake", "Pastry"],
    "Beverage": ["Hot Drink", "Cold Drink", "Alcoholic"],
    "Side Dish": ["Salad", "Bread", "Vegetables"]
  };

  const handleSave = () => {
    onSave(formData);
    onClose();
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Reset subcategory when category changes
    if (field === 'category') {
      setFormData(prev => ({
        ...prev,
        category: value,
        subCategory: ""
      }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl border-2 border-orange-200">
        <DialogHeader className="bg-gradient-to-r from-orange-100 to-orange-50 -m-6 mb-6 p-6 border-b-2 border-orange-200">
          <DialogTitle className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <ChefHat className="w-6 h-6 text-orange-600" />
            Season Menu Item: {itemName}
            <Sparkles className="w-4 h-4 text-orange-500" />
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-2 gap-6 py-4">
          {/* Left Column */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="itemName" className="text-sm font-medium text-gray-700 mb-2 block">
                Item Name
              </Label>
              <div className="relative">
                <Input
                  id="itemName"
                  value={formData.itemName}
                  onChange={(e) => handleInputChange('itemName', e.target.value)}
                  className="pr-10 border-2 border-orange-200 hover:border-orange-300 focus:border-orange-500"
                />
                <Edit className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>
            </div>

            <div>
              <Label htmlFor="category" className="text-sm font-medium text-gray-700 mb-2 block">
                Menu Item Category
              </Label>
              <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                <SelectTrigger className="border-2 border-orange-200 hover:border-orange-300 focus:border-orange-500">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent className="border-2 border-orange-200">
                  {categories.map((category) => (
                    <SelectItem key={category} value={category} className="hover:bg-orange-50">
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="subCategory" className="text-sm font-medium text-gray-700 mb-2 block">
                Menu Item Sub-Category
              </Label>
              <Select 
                value={formData.subCategory} 
                onValueChange={(value) => handleInputChange('subCategory', value)}
                disabled={!formData.category}
              >
                <SelectTrigger className="border-2 border-orange-200 hover:border-orange-300 focus:border-orange-500">
                  <SelectValue placeholder="Entree" />
                </SelectTrigger>
                <SelectContent className="border-2 border-orange-200">
                  {formData.category && subCategories[formData.category]?.map((subCategory) => (
                    <SelectItem key={subCategory} value={subCategory} className="hover:bg-orange-50">
                      {subCategory}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="tags" className="text-sm font-medium text-gray-700 mb-2 block">
                Tags
              </Label>
              <Textarea
                id="tags"
                placeholder="Type a tag and hit return"
                value={formData.tags}
                onChange={(e) => handleInputChange('tags', e.target.value)}
                className="min-h-[80px] resize-none border-2 border-orange-200 hover:border-orange-300 focus:border-orange-500"
              />
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="itemSKU" className="text-sm font-medium text-gray-700 mb-2 block">
                Item SKU
              </Label>
              <Input
                id="itemSKU"
                value={formData.itemSKU}
                onChange={(e) => handleInputChange('itemSKU', e.target.value)}
                className="border-2 border-orange-200 hover:border-orange-300 focus:border-orange-500"
              />
            </div>

            <div>
              <Label htmlFor="itemPLU" className="text-sm font-medium text-gray-700 mb-2 block">
                Item PLU
              </Label>
              <Input
                id="itemPLU"
                value={formData.itemPLU}
                onChange={(e) => handleInputChange('itemPLU', e.target.value)}
                className="border-2 border-orange-200 hover:border-orange-300 focus:border-orange-500"
              />
            </div>

            <div>
              <Label htmlFor="receivedItemName" className="text-sm font-medium text-gray-700 mb-2 block flex items-center gap-1">
                <span>🥕</span>
                Received Item Name (Source System)
              </Label>
              <Input
                id="receivedItemName"
                value={formData.receivedItemName}
                readOnly
                className="bg-gradient-to-r from-orange-50 to-orange-100 border-2 border-orange-200 text-gray-700 cursor-not-allowed"
                title="This field shows the exact name from the source system and cannot be edited"
              />
              <p className="text-xs text-gray-500 mt-1">
                This is the exact name as received from the source system
              </p>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-4 border-t-2 border-orange-200">
          <Button variant="outline" onClick={onClose} className="border-2 border-gray-200 hover:bg-gray-50">
            Close
          </Button>
          <Button 
            onClick={handleSave}
            className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white border-0"
          >
            <ChefHat className="w-4 h-4 mr-1" />
            Save Recipe
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
