
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { X, ArrowLeft, ArrowRight, Sparkles, ArrowDown, ArrowUp, ArrowLeftIcon, ArrowRightIcon } from 'lucide-react';
import { WalkthroughStep } from '@/types/walkthrough';

interface WalkthroughCardProps {
  step: WalkthroughStep;
  currentStep: number;
  totalSteps: number;
  cardPosition: { top: number; left: number };
  arrowDirection: 'up' | 'down' | 'left' | 'right';
  onNext: () => void;
  onPrevious: () => void;
  onSkip: () => void;
}

export const WalkthroughCard: React.FC<WalkthroughCardProps> = ({
  step,
  currentStep,
  totalSteps,
  cardPosition,
  arrowDirection,
  onNext,
  onPrevious,
  onSkip
}) => {
  return (
    <Card 
      className="fixed z-50 w-96 bg-white border-4 border-orange-400 shadow-2xl animate-scale-in"
      style={{
        top: cardPosition.top,
        left: cardPosition.left,
        transform: 'scale(1.02)',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(234, 88, 12, 0.1)'
      }}
    >
      {/* Arrow pointing to highlighted element */}
      <div 
        className="absolute text-orange-500 animate-bounce"
        style={{
          ...(arrowDirection === 'up' && { bottom: '100%', left: '50%', transform: 'translateX(-50%)' }),
          ...(arrowDirection === 'down' && { top: '100%', left: '50%', transform: 'translateX(-50%)' }),
          ...(arrowDirection === 'left' && { right: '100%', top: '50%', transform: 'translateY(-50%)' }),
          ...(arrowDirection === 'right' && { left: '100%', top: '50%', transform: 'translateY(-50%)' }),
        }}
      >
        {arrowDirection === 'up' && <ArrowUp className="w-8 h-8" />}
        {arrowDirection === 'down' && <ArrowDown className="w-8 h-8" />}
        {arrowDirection === 'left' && <ArrowLeftIcon className="w-8 h-8" />}
        {arrowDirection === 'right' && <ArrowRightIcon className="w-8 h-8" />}
      </div>

      <CardContent className="p-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-100 rounded-full">
              <Sparkles className="w-6 h-6 text-orange-600" />
            </div>
            <span className="text-lg font-bold text-orange-600">
              Step {currentStep + 1} of {totalSteps}
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onSkip}
            className="text-gray-500 hover:text-gray-700 hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Enhanced Progress Bar */}
        <div className="w-full bg-orange-100 rounded-full h-3 mb-6 overflow-hidden">
          <div 
            className="bg-gradient-to-r from-orange-500 to-orange-600 h-3 rounded-full transition-all duration-500 ease-out relative"
            style={{ width: `${((currentStep + 1) / totalSteps) * 100}%` }}
          >
            <div className="absolute inset-0 bg-white bg-opacity-30 animate-pulse" />
          </div>
        </div>

        {/* Content */}
        <div className="mb-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-4 leading-tight">
            {step.title}
          </h3>
          <p className="text-gray-700 text-base leading-relaxed">
            {step.description}
          </p>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="outline"
            size="default"
            onClick={onPrevious}
            disabled={currentStep === 0}
            className="flex items-center gap-2 border-2 border-orange-200 hover:border-orange-300"
          >
            <ArrowLeft className="w-4 h-4" />
            Previous
          </Button>

          <Button
            onClick={onNext}
            size="default"
            className="flex items-center gap-2 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-semibold px-6 py-3 shadow-lg"
          >
            {currentStep === totalSteps - 1 ? (
              <>
                Complete Tour
                <Sparkles className="w-4 h-4" />
              </>
            ) : (
              <>
                Next
                <ArrowRight className="w-4 h-4" />
              </>
            )}
          </Button>
        </div>

        {/* Skip Option */}
        <div className="text-center">
          <button
            onClick={onSkip}
            className="text-sm text-gray-500 hover:text-gray-700 underline hover:no-underline transition-all"
          >
            Skip this tour
          </button>
        </div>
      </CardContent>
    </Card>
  );
};
