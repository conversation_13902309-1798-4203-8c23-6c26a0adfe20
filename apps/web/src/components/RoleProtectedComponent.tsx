
import { ReactNode } from 'react';
import { useUserProfile } from '@/hooks/useUserProfile';
import { useAuth } from '@/hooks/useAuth';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield } from 'lucide-react';

interface RoleProtectedComponentProps {
  allowedRoles: string[];
  children: ReactNode;
  fallback?: ReactNode;
  showAccessDenied?: boolean;
}

export function RoleProtectedComponent({ 
  allowedRoles, 
  children, 
  fallback, 
  showAccessDenied = true 
}: RoleProtectedComponentProps) {
  const { user } = useAuth();
  const { profile } = useUserProfile();

  // Show loading if user is still being fetched
  if (!user) {
    return null;
  }

  // Show loading if profile is still being fetched
  if (user && !profile) {
    return <div className="animate-pulse h-4 bg-gray-200 rounded"></div>;
  }

  // Check if user has required role
  const hasRequiredRole = profile?.role && allowedRoles.includes(profile.role);

  if (!hasRequiredRole) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (showAccessDenied) {
      return (
        <Alert className="border-orange-200 bg-orange-50">
          <Shield className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            Access restricted. This feature requires {allowedRoles.length === 1 ? 'a' : 'one of the following'} role
            {allowedRoles.length > 1 ? 's' : ''}: {allowedRoles.join(', ')}.
          </AlertDescription>
        </Alert>
      );
    }

    return null;
  }

  return <>{children}</>;
}
