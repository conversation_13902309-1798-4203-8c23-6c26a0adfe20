
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { TrendingUp, Users, DollarSign, Target, MapPin, Calendar } from "lucide-react";
import { useSalesTransactions, useRestaurantLocations } from "@/hooks/useRestaurantData";
import { Skeleton } from "@/components/ui/skeleton";
import { useMemo } from "react";

interface LocationMetricsCardsProps {
  locationId: string | null;
}

export const LocationMetricsCards = ({ locationId }: LocationMetricsCardsProps) => {
  const { data: transactions, isLoading } = useSalesTransactions(locationId, 1000);
  const { data: locations } = useRestaurantLocations();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Calculate comprehensive metrics from transactions
  const metrics = useMemo(() => {
    if (!transactions || transactions.length === 0) {
      return { 
        totalRevenue: 0, 
        transactionCount: 0, 
        averageTicket: 0, 
        customerCount: 0,
        dailyAverage: 0,
        growthRate: 0
      };
    }

    const totalRevenue = transactions.reduce((sum, t) => sum + Number(t.total_amount), 0);
    const transactionCount = transactions.length;
    const customerCount = transactions.reduce((sum, t) => sum + (t.customer_count || 1), 0);
    const averageTicket = transactionCount > 0 ? totalRevenue / transactionCount : 0;
    
    // Calculate daily average (assuming last 30 days)
    const daySpan = Math.max(1, Math.ceil((Date.now() - new Date(transactions[transactions.length - 1]?.transaction_date || Date.now()).getTime()) / (1000 * 60 * 60 * 24)));
    const dailyAverage = totalRevenue / Math.min(daySpan, 30);
    
    // Calculate growth rate (mock calculation based on recent vs older transactions)
    const midPoint = Math.floor(transactions.length / 2);
    const recentRevenue = transactions.slice(0, midPoint).reduce((sum, t) => sum + Number(t.total_amount), 0);
    const olderRevenue = transactions.slice(midPoint).reduce((sum, t) => sum + Number(t.total_amount), 0);
    const growthRate = olderRevenue > 0 ? ((recentRevenue - olderRevenue) / olderRevenue) * 100 : 0;

    return {
      totalRevenue,
      transactionCount,
      averageTicket,
      customerCount,
      dailyAverage,
      growthRate
    };
  }, [transactions]);

  const viewType = locationId ? "Location" : "Portfolio";
  const selectedLocation = locations?.find(loc => loc.id === locationId);
  const locationCount = locations?.length || 0;

  return (
    <div className="space-y-6">
      {/* Location Info Banner */}
      {selectedLocation && (
        <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <MapPin className="w-6 h-6 text-blue-600" />
              <div>
                <h3 className="font-semibold text-gray-900">{selectedLocation.name}</h3>
                <p className="text-sm text-gray-600">
                  {selectedLocation.address}, {selectedLocation.city}, {selectedLocation.state} {selectedLocation.zip_code}
                </p>
                {selectedLocation.manager_name && (
                  <p className="text-sm text-gray-500">Manager: {selectedLocation.manager_name}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50 hover:shadow-lg hover:shadow-orange-100/50 transition-all">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">{viewType} Revenue</CardTitle>
            <div className="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
              <DollarSign className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <>
                <div className="text-2xl font-bold text-gray-900">
                  {formatCurrency(metrics.totalRevenue)}
                </div>
                <p className="text-xs text-orange-600 font-medium">
                  📊 {metrics.transactionCount} transactions
                  {locationCount > 1 && !locationId && ` • ${locationCount} locations`}
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50 hover:shadow-lg hover:shadow-orange-100/50 transition-all">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">Average Ticket</CardTitle>
            <div className="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
              <Target className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <>
                <div className="text-2xl font-bold text-gray-900">
                  {formatCurrency(metrics.averageTicket)}
                </div>
                <p className="text-xs text-green-600 font-medium">
                  🎯 Per transaction average
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50 hover:shadow-lg hover:shadow-orange-100/50 transition-all">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">Customer Count</CardTitle>
            <div className="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
              <Users className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold text-gray-900">{metrics.customerCount.toLocaleString()}</div>
                <p className="text-xs text-blue-600 font-medium">
                  👥 Total customers served
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50 hover:shadow-lg hover:shadow-orange-100/50 transition-all">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">Daily Average</CardTitle>
            <div className="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
              <Calendar className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold text-gray-900">
                  {formatCurrency(metrics.dailyAverage)}
                </div>
                <p className="text-xs text-purple-600 font-medium">
                  📅 Daily revenue average
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Performance Insights */}
      {!isLoading && metrics.transactionCount > 0 && (
        <Card className="border-2 border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-800">
              <TrendingUp className="w-5 h-5" />
              Performance Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center p-3 bg-white rounded-lg border border-green-200">
                <div className="font-semibold text-green-700">Growth Rate</div>
                <div className={`text-lg font-bold ${metrics.growthRate >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {metrics.growthRate >= 0 ? '+' : ''}{metrics.growthRate.toFixed(1)}%
                </div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg border border-green-200">
                <div className="font-semibold text-green-700">Avg Customers/Transaction</div>
                <div className="text-lg font-bold text-green-600">
                  {(metrics.customerCount / metrics.transactionCount).toFixed(1)}
                </div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg border border-green-200">
                <div className="font-semibold text-green-700">Transaction Volume</div>
                <div className="text-lg font-bold text-green-600">
                  {metrics.transactionCount > 100 ? 'High' : metrics.transactionCount > 50 ? 'Medium' : 'Low'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
