
import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useRestaurantLocations } from "@/hooks/useRestaurantData";
import { Target, Building2, Check, TrendingUp, Info, ArrowRight } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface LocationMatchingModalProps {
  isOpen: boolean;
  onClose: () => void;
  unmatchedLocation?: {
    source_value: string;
    frequency?: number;
  };
}

export const LocationMatchingModal = ({ isOpen, onClose, unmatchedLocation }: LocationMatchingModalProps) => {
  const [selectedLocationId, setSelectedLocationId] = useState<string>("");
  const [isCreatingNew, setIsCreatingNew] = useState(false);
  const [newLocationData, setNewLocationData] = useState({
    name: unmatchedLocation?.source_value || "",
    address: "",
    city: "",
    state: "",
    zip_code: "",
    manager_name: "",
    phone: ""
  });

  const { data: locations } = useRestaurantLocations();
  const { toast } = useToast();

  const businessBenefits = [
    "📊 Unified location-based reporting",
    "🎯 Accurate multi-location analytics", 
    "📈 Performance comparison insights",
    "💰 Location-specific profitability tracking"
  ];

  const handleMatchToExisting = () => {
    if (!selectedLocationId) return;
    
    const selectedLocation = locations?.find(loc => loc.id === selectedLocationId);
    if (selectedLocation) {
      toast({
        title: "Location Matched Successfully",
        description: `"${unmatchedLocation?.source_value}" has been matched to ${selectedLocation.name}`,
      });
      onClose();
    }
  };

  const handleCreateNew = () => {
    if (!newLocationData.name || !newLocationData.city) {
      toast({
        title: "Missing Information",
        description: "Please fill in at least the location name and city.",
        variant: "destructive"
      });
      return;
    }

    toast({
      title: "New Location Created",
      description: `"${newLocationData.name}" has been created and matched successfully.`,
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="bg-gradient-to-r from-blue-100 to-blue-50 -m-6 mb-6 p-6 border-b-2 border-blue-200">
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Target className="w-6 h-6 text-blue-600" />
            Standardize Location Data: {unmatchedLocation?.source_value}
          </DialogTitle>
          <DialogDescription className="text-blue-700">
            Unify location data across all your systems for accurate multi-location reporting and analytics.
            {unmatchedLocation?.frequency && (
              <span className="block mt-1 text-sm font-medium">
                This location appears {unmatchedLocation.frequency} times in your data
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Left Column - Business Value */}
          <div className="lg:col-span-1 space-y-4">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border-2 border-blue-200">
              <div className="flex items-center gap-2 mb-3">
                <TrendingUp className="w-5 h-5 text-blue-600" />
                <h3 className="font-semibold text-blue-800">Why This Matters</h3>
              </div>
              <p className="text-sm text-blue-700 mb-3">
                Matching locations correctly ensures all your data from different systems refers to the same physical location.
              </p>
              <div className="space-y-2">
                {businessBenefits.map((benefit, index) => (
                  <div key={index} className="text-xs text-blue-600 flex items-center gap-1">
                    <Check className="w-3 h-3" />
                    {benefit}
                  </div>
                ))}
              </div>
            </div>

            <Alert className="border-orange-200 bg-orange-50">
              <Info className="w-4 h-4 text-orange-600" />
              <AlertDescription className="text-sm text-orange-800">
                <strong>Tip:</strong> Match to existing if this is the same location with different naming (e.g., "Downtown" vs "Downtown Location").
              </AlertDescription>
            </Alert>
          </div>

          {/* Right Column - Matching Interface */}
          <div className="lg:col-span-2 space-y-6">
            {/* Process Steps */}
            <div className="bg-gray-50 rounded-lg p-4 border">
              <h4 className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                <Target className="w-4 h-4" />
                Choose Your Action
              </h4>
              
              {/* Option 1: Match to Existing */}
              <div className={`border-2 rounded-lg p-4 mb-4 transition-all ${!isCreatingNew ? 'border-blue-300 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`}>
                <div className="flex items-center gap-2 mb-3">
                  <Button
                    variant={!isCreatingNew ? "default" : "outline"}
                    size="sm"
                    onClick={() => setIsCreatingNew(false)}
                    className="flex items-center gap-2"
                  >
                    <Building2 className="w-4 h-4" />
                    Match to Existing Location
                    {!isCreatingNew && <ArrowRight className="w-4 h-4" />}
                  </Button>
                </div>
                <p className="text-xs text-gray-600 mb-3">
                  Use this when the location already exists but has a different name in your system
                </p>
                
                {!isCreatingNew && (
                  <div className="space-y-3">
                    <Label htmlFor="existing-location">Select Location</Label>
                    <Select value={selectedLocationId} onValueChange={setSelectedLocationId}>
                      <SelectTrigger className="border-2 border-blue-200">
                        <SelectValue placeholder="Choose an existing location..." />
                      </SelectTrigger>
                      <SelectContent>
                        {locations?.map((location) => (
                          <SelectItem key={location.id} value={location.id}>
                            <div className="flex items-center gap-2">
                              <Target className="w-4 h-4" />
                              {location.name}
                              {location.city && (
                                <span className="text-sm text-gray-500">({location.city})</span>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button 
                      onClick={handleMatchToExisting} 
                      disabled={!selectedLocationId} 
                      className="w-full bg-blue-600 hover:bg-blue-700"
                    >
                      <Check className="w-4 h-4 mr-2" />
                      Match to Selected Location
                    </Button>
                  </div>
                )}
              </div>

              {/* Option 2: Create New */}
              <div className={`border-2 rounded-lg p-4 transition-all ${isCreatingNew ? 'border-green-300 bg-green-50' : 'border-gray-200 hover:border-gray-300'}`}>
                <div className="flex items-center gap-2 mb-3">
                  <Button
                    variant={isCreatingNew ? "default" : "outline"}
                    size="sm"
                    onClick={() => setIsCreatingNew(true)}
                    className="flex items-center gap-2"
                  >
                    <Target className="w-4 h-4" />
                    Create New Location
                    {isCreatingNew && <ArrowRight className="w-4 h-4" />}
                  </Button>
                </div>
                <p className="text-xs text-gray-600 mb-3">
                  Use this when this is truly a new location not in your system
                </p>
                
                {isCreatingNew && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Location Name *</Label>
                      <Input
                        id="name"
                        value={newLocationData.name}
                        onChange={(e) => setNewLocationData({...newLocationData, name: e.target.value})}
                        placeholder="Enter location name"
                        className="border-2 border-green-200"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="address">Address</Label>
                      <Input
                        id="address"
                        value={newLocationData.address}
                        onChange={(e) => setNewLocationData({...newLocationData, address: e.target.value})}
                        placeholder="Street address"
                        className="border-2 border-green-200"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="city">City *</Label>
                      <Input
                        id="city"
                        value={newLocationData.city}
                        onChange={(e) => setNewLocationData({...newLocationData, city: e.target.value})}
                        placeholder="City"
                        className="border-2 border-green-200"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="state">State</Label>
                      <Input
                        id="state"
                        value={newLocationData.state}
                        onChange={(e) => setNewLocationData({...newLocationData, state: e.target.value})}
                        placeholder="State"
                        className="border-2 border-green-200"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="zip">ZIP Code</Label>
                      <Input
                        id="zip"
                        value={newLocationData.zip_code}
                        onChange={(e) => setNewLocationData({...newLocationData, zip_code: e.target.value})}
                        placeholder="ZIP code"
                        className="border-2 border-green-200"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="manager">Manager Name</Label>
                      <Input
                        id="manager"
                        value={newLocationData.manager_name}
                        onChange={(e) => setNewLocationData({...newLocationData, manager_name: e.target.value})}
                        placeholder="Manager name"
                        className="border-2 border-green-200"
                      />
                    </div>
                    <div className="space-y-2 md:col-span-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        value={newLocationData.phone}
                        onChange={(e) => setNewLocationData({...newLocationData, phone: e.target.value})}
                        placeholder="Phone number"
                        className="border-2 border-green-200"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <Button 
                        onClick={handleCreateNew} 
                        className="w-full bg-green-600 hover:bg-green-700"
                      >
                        <Check className="w-4 h-4 mr-2" />
                        Create and Match Location
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* What Happens Next */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200">
              <h4 className="font-medium text-green-800 mb-2 flex items-center gap-2">
                <Check className="w-4 h-4" />
                What Happens Next
              </h4>
              <div className="text-sm text-green-700 space-y-1">
                <div>✅ All data will be unified under the selected location</div>
                <div>📊 Location-based reports will show consolidated data</div>
                <div>🎯 Future data will auto-match to this location</div>
                <div>📈 You'll see improved location analytics accuracy</div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
