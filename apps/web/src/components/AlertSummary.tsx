
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>gle, Clock, CheckCircle, XCircle } from "lucide-react";
import { <PERSON> } from "react-router-dom";

const recentAlerts = [
  {
    id: 1,
    title: "High failure rate detected",
    location: "West Side - Taco Time",
    severity: "high",
    time: "5 min ago",
    status: "active"
  },
  {
    id: 2,
    title: "Sync delay warning",
    location: "Mall Location - Burger Bros",
    severity: "medium",
    time: "12 min ago",
    status: "active"
  },
  {
    id: 3,
    title: "Data quality improvement",
    location: "Downtown Main - Pizza Palace",
    severity: "low",
    time: "1 hour ago",
    status: "resolved"
  }
];

const getSeverityBadge = (severity: string) => {
  switch (severity) {
    case "high":
      return <Badge className="bg-red-100 text-red-800">High</Badge>;
    case "medium":
      return <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>;
    case "low":
      return <Badge className="bg-blue-100 text-blue-800">Low</Badge>;
    default:
      return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case "active":
      return <XCircle className="w-4 h-4 text-red-500" />;
    case "resolved":
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    default:
      return <Clock className="w-4 h-4 text-yellow-500" />;
  }
};

export function AlertSummary() {
  return (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2 text-orange-500" />
            Recent Alerts
          </CardTitle>
          <p className="text-sm text-gray-600">Last 24 hours activity</p>
        </div>
        <Link to="/alerts">
          <Button variant="outline" size="sm">
            View All
          </Button>
        </Link>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentAlerts.map((alert) => (
            <div key={alert.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-start space-x-3">
                {getStatusIcon(alert.status)}
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{alert.title}</h4>
                  <p className="text-sm text-gray-600">{alert.location}</p>
                  <p className="text-xs text-gray-500">{alert.time}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {getSeverityBadge(alert.severity)}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
