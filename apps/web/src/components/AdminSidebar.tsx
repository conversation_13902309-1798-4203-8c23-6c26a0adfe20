import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Home, Database, Zap, AlertTriangle, Settings, User, ChevronDown, ChevronRight } from 'lucide-react';
import { ConvxLogo } from '@/components/ConvxLogo';
import { ConvxOpsLogo } from '@/components/ConvxOpsLogo';
import { ConvxMarketingLogo } from '@/components/ConvxMarketingLogo';

const mainNavigation = [{
  name: 'Dashboard',
  href: '/',
  icon: Home
}, {
  name: 'Master Data',
  href: '/master-data',
  icon: Database
}, {
  name: 'Integrations',
  href: '/integrations',
  icon: Zap
}, {
  name: 'Alerts',
  href: '/alerts',
  icon: AlertTriangle
}];

const convxSubMenus = [{
  name: 'CONVX Ops',
  href: '/convx-ops',
  logo: ConvxOps<PERSON>ogo
}, {
  name: 'CONVX Marketing',
  href: '/convx-marketing',
  logo: ConvxMarketingLogo
}, {
  name: 'CONVX Finance',
  href: '/convx-finance',
  logo: ConvxLogo // Using the original orange logo for Finance
}];

const bottomNavigation = [{
  name: 'Settings',
  href: '/settings',
  icon: Settings
}, {
  name: 'Profile',
  href: '/profile',
  icon: User
}];

export function AdminSidebar() {
  const location = useLocation();
  const [isHeaderDropdownExpanded, setIsHeaderDropdownExpanded] = useState(false);

  // Check if any CONVX submenu is active
  const isConvxActive = convxSubMenus.some(item => location.pathname === item.href);

  return (
    <div className="w-64 bg-white shadow-lg flex flex-col">
      {/* Header - Now clickable with dropdown */}
      <div className="border-b border-gray-200">
        <button 
          onClick={() => setIsHeaderDropdownExpanded(!isHeaderDropdownExpanded)}
          className={cn(
            'w-full p-6 text-left transition-colors hover:bg-orange-50',
            isConvxActive || isHeaderDropdownExpanded ? 'bg-orange-50' : ''
          )}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <ConvxLogo width={40} height={40} />
              <div>
                <h2 className="text-xl font-bold text-gray-900">CONVX Data</h2>
                <p className="text-sm text-gray-600">Admin Dashboard</p>
              </div>
            </div>
            {isHeaderDropdownExpanded ? (
              <ChevronDown className="h-5 w-5 text-gray-400" />
            ) : (
              <ChevronRight className="h-5 w-5 text-gray-400" />
            )}
          </div>
        </button>

        {/* CONVX Services Dropdown */}
        {isHeaderDropdownExpanded && (
          <div className="px-4 pb-4 space-y-1 bg-orange-25 border-t border-orange-100">
            {convxSubMenus.map(item => {
              const isActive = location.pathname === item.href;
              const LogoComponent = item.logo;
              return (
                <Link 
                  key={item.name} 
                  to={item.href} 
                  className={cn(
                    'flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors',
                    isActive 
                      ? 'bg-orange-100 text-orange-800 border-r-2 border-orange-700' 
                      : 'text-gray-600 hover:bg-orange-50 hover:text-orange-600'
                  )}
                >
                  <LogoComponent width={20} height={20} className="mr-3" />
                  {item.name}
                </Link>
              );
            })}
          </div>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {/* Main Navigation - No more CONVX Data dropdown here */}
        {mainNavigation.map(item => {
          const isActive = location.pathname === item.href;
          return (
            <Link 
              key={item.name} 
              to={item.href} 
              className={cn(
                'flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors',
                isActive 
                  ? 'bg-orange-50 text-orange-700 border-r-2 border-orange-700' 
                  : 'text-gray-700 hover:bg-orange-50 hover:text-orange-600'
              )}
            >
              <item.icon className={cn('mr-3 h-5 w-5', isActive ? 'text-orange-700' : 'text-gray-400')} />
              {item.name}
            </Link>
          );
        })}

        {/* Bottom Navigation */}
        <div className="pt-4 mt-4 border-t border-gray-200">
          {bottomNavigation.map(item => {
            const isActive = location.pathname === item.href;
            return (
              <Link 
                key={item.name} 
                to={item.href} 
                className={cn(
                  'flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors',
                  isActive 
                    ? 'bg-orange-50 text-orange-700 border-r-2 border-orange-700' 
                    : 'text-gray-700 hover:bg-orange-50 hover:text-orange-600'
                )}
              >
                <item.icon className={cn('mr-3 h-5 w-5', isActive ? 'text-orange-700' : 'text-gray-400')} />
                {item.name}
              </Link>
            );
          })}
        </div>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="text-xs text-gray-500 text-center">
          CONVX Data Platform v2.0
        </div>
      </div>
    </div>
  );
}
