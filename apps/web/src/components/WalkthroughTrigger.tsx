
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { HelpCircle, Play } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface WalkthroughTriggerProps {
  onStart: () => void;
  className?: string;
}

export const WalkthroughTrigger: React.FC<WalkthroughTriggerProps> = ({ 
  onStart, 
  className 
}) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            onClick={onStart}
            className={className || "fixed bottom-6 right-6 z-30 bg-white border-2 border-orange-300 text-orange-600 hover:bg-orange-50 shadow-lg hover:shadow-xl transition-all duration-200"}
          >
            <Play className="w-4 h-4 mr-2" />
            Take Tour
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Take a guided tour of the platform</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
