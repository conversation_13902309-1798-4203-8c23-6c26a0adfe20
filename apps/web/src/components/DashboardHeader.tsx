import { useState } from "react";
import { <PERSON>, Bell, ChevronDown, LogOut, Menu } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { NotificationModal } from "@/components/notifications/NotificationModal";
import { CategoryItemModal } from "@/components/notifications/CategoryItemModal";
import { sampleNotifications } from "@/data/sampleNotifications";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
export function DashboardHeader() {
  const [isNotificationModalOpen, setIsNotificationModalOpen] = useState(false);
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<string>("");
  const [notifications, setNotifications] = useState(sampleNotifications);
  const {
    toast
  } = useToast();
  const {
    user,
    signOut
  } = useAuth();
  const unreadCount = notifications.filter(n => !n.isRead).length;
  const handleCategorizeItem = (notificationId: string) => {
    const notification = notifications.find(n => n.id === notificationId);
    if (notification?.itemName) {
      setSelectedItem(notification.itemName);
      setIsNotificationModalOpen(false);
      setIsCategoryModalOpen(true);
    }
  };
  const handleSaveCategorization = (categoryData: any) => {
    console.log("Saving categorization:", categoryData);
    toast({
      title: "Item Categorized",
      description: `${categoryData.itemName} has been successfully categorized as ${categoryData.category}.`
    });

    // Mark the notification as read and remove it
    setNotifications(prev => prev.filter(n => n.itemName !== selectedItem));
  };
  const handleMarkAsSeen = (notificationId: string) => {
    setNotifications(prev => prev.map(n => n.id === notificationId ? {
      ...n,
      isRead: true
    } : n));
    toast({
      title: "Marked as seen",
      description: "Notification has been marked as read."
    });
  };
  const handleSignOut = async () => {
    await signOut();
    toast({
      title: "Signed out",
      description: "You have been successfully signed out."
    });
  };
  return <>
      <header className="bg-gradient-to-r from-white to-orange-50 border-b-2 border-orange-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            
            
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-orange-400 w-4 h-4" />
              <Input placeholder="Search data, integrations, analytics..." className="pl-10 w-80 bg-white border-2 border-orange-200 focus:border-orange-400 hover:border-orange-300" />
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" className="relative hover:bg-orange-100 text-orange-600 hover:text-orange-700" onClick={() => setIsNotificationModalOpen(true)}>
              <Bell className="w-5 h-5" />
              {unreadCount > 0 && <span className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full text-xs text-white flex items-center justify-center">
                  {unreadCount}
                </span>}
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center space-x-2 cursor-pointer hover:bg-orange-50 p-2 rounded-lg border-2 border-transparent hover:border-orange-200 transition-colors">
                  <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">
                      {user?.email?.charAt(0).toUpperCase() || 'U'}
                    </span>
                  </div>
                  <ChevronDown className="w-4 h-4 text-orange-600" />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56 border-2 border-orange-200 bg-white">
                <DropdownMenuItem onClick={handleSignOut} className="hover:bg-orange-50 focus:bg-orange-50">
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      <NotificationModal isOpen={isNotificationModalOpen} onClose={() => setIsNotificationModalOpen(false)} notifications={notifications} onCategorizeItem={handleCategorizeItem} onMarkAsSeen={handleMarkAsSeen} />

      <CategoryItemModal isOpen={isCategoryModalOpen} onClose={() => setIsCategoryModalOpen(false)} itemName={selectedItem} onSave={handleSaveCategorization} />
    </>;
}