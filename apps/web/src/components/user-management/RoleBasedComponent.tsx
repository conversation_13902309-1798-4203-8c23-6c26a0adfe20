
import { ReactNode } from 'react';
import { useUserProfile } from '@/hooks/useUserProfile';
import { User } from '@/types/onboarding';

interface RoleBasedComponentProps {
  allowedRoles: User['role'][];
  children: ReactNode;
  fallback?: ReactNode;
}

export function RoleBasedComponent({ allowedRoles, children, fallback = null }: RoleBasedComponentProps) {
  const { profile } = useUserProfile();

  if (!profile || !allowedRoles.includes(profile.role)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
