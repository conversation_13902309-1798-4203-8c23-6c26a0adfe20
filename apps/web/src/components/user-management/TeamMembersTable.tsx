
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useTeamInvitations } from '@/hooks/useTeamInvitations';
import { InviteTeamMemberModal } from './InviteTeamMemberModal';
import { RoleProtectedComponent } from '@/components/RoleProtectedComponent';
import { Clock, CheckCircle, XCircle, RotateCcw, Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export function TeamMembersTable() {
  const { invitations, loading, resendInvitation, cancelInvitation } = useTeamInvitations();
  const { toast } = useToast();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'accepted':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'expired':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'accepted':
        return <Badge className="bg-green-100 text-green-800">Accepted</Badge>;
      case 'expired':
        return <Badge className="bg-red-100 text-red-800">Expired</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
    }
  };

  const handleResend = async (invitationId: string, email: string) => {
    try {
      await resendInvitation(invitationId);
      toast({
        title: "Invitation resent",
        description: `Invitation resent to ${email}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to resend invitation.",
        variant: "destructive"
      });
    }
  };

  const handleCancel = async (invitationId: string, email: string) => {
    try {
      await cancelInvitation(invitationId);
      toast({
        title: "Invitation canceled",
        description: `Invitation to ${email} has been canceled`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to cancel invitation.",
        variant: "destructive"
      });
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Team Members</CardTitle>
        <RoleProtectedComponent allowedRoles={['CEO', 'COO', 'Data Admin']}>
          <InviteTeamMemberModal />
        </RoleProtectedComponent>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-8">Loading team members...</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Invited</TableHead>
                <TableHead>Expires</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {invitations.map((invitation) => (
                <TableRow key={invitation.id}>
                  <TableCell>{invitation.email}</TableCell>
                  <TableCell>{invitation.role}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(invitation.status)}
                      {getStatusBadge(invitation.status)}
                    </div>
                  </TableCell>
                  <TableCell>
                    {invitation.createdAt.toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {invitation.expiresAt.toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <RoleProtectedComponent 
                      allowedRoles={['CEO', 'COO', 'Data Admin']}
                      showAccessDenied={false}
                    >
                      <div className="flex items-center space-x-1">
                        {invitation.status === 'pending' && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleResend(invitation.id, invitation.email)}
                            >
                              <RotateCcw className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCancel(invitation.id, invitation.email)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </>
                        )}
                        {invitation.status === 'expired' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleResend(invitation.id, invitation.email)}
                          >
                            <RotateCcw className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </RoleProtectedComponent>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
        
        {!loading && invitations.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No team invitations yet. 
            <RoleProtectedComponent 
              allowedRoles={['CEO', 'COO', 'Data Admin']}
              showAccessDenied={false}
            >
              Invite your first team member!
            </RoleProtectedComponent>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
