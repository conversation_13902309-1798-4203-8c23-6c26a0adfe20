
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Download } from 'lucide-react';

export function ProjectScopeExport() {
  const handleDownloadCSV = () => {
    const csvUrl = '/convx-project-scope.csv';
    const link = document.createElement('a');
    link.href = csvUrl;
    link.download = 'CONVX-Data-Platform-Project-Scope.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="bg-white rounded-lg p-6 border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Project Scope Export
      </h3>
      <p className="text-gray-600 mb-4">
        Download the complete project scope with detailed tasks, timelines, and resource allocation for the CONVX Data Platform development.
      </p>
      <Button 
        onClick={handleDownloadCSV}
        className="flex items-center gap-2"
      >
        <Download className="w-4 h-4" />
        Download Project Scope CSV
      </Button>
    </div>
  );
}
