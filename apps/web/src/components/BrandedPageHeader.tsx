
import React from 'react';
import { LucideIcon } from 'lucide-react';
import { ChefHat, Sparkles } from 'lucide-react';

interface BrandedPageHeaderProps {
  title: string;
  description: string;
  icon: LucideIcon;
  className?: string;
  children?: React.ReactNode;
}

export function BrandedPageHeader({ title, description, icon: Icon, className = "", children }: BrandedPageHeaderProps) {
  return (
    <div className={`relative ${className}`}>
      <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl p-8 text-white relative overflow-hidden">
        <div className="absolute top-0 right-0 opacity-10">
          <Icon className="w-32 h-32" />
        </div>
        <div className="absolute bottom-0 left-0 opacity-10">
          <ChefHat className="w-24 h-24" />
        </div>
        <div className="relative z-10 flex justify-between items-start">
          <div>
            <div className="flex items-center gap-3 mb-4">
              <Icon className="w-8 h-8" />
              <h1 className="text-4xl font-bold">{title}</h1>
              <Sparkles className="w-6 h-6 text-orange-200" />
            </div>
            <p className="text-orange-100 text-lg max-w-2xl">
              {description}
            </p>
          </div>
          {children && (
            <div className="flex-shrink-0 ml-6">
              {children}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
