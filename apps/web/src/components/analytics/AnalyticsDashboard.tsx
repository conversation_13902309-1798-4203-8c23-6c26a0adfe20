
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { useAnalytics } from '@/hooks/useAnalytics';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { TrendingUp, DollarSign, ShoppingCart, Users } from 'lucide-react';

interface AnalyticsDashboardProps {
  locationId?: string;
  dateRange?: { start: string; end: string };
}

export function AnalyticsDashboard({ locationId, dateRange }: AnalyticsDashboardProps) {
  const { data: analytics, isLoading } = useAnalytics(locationId, dateRange);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-24 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!analytics) return null;

  const kpiCards = [
    {
      title: 'Total Sales',
      value: `$${analytics.totalSales.toLocaleString()}`,
      icon: DollarSign,
      change: '+12.5%',
      changeType: 'positive' as const
    },
    {
      title: 'Transactions',
      value: analytics.transactionCount.toLocaleString(),
      icon: ShoppingCart,
      change: '+8.2%',
      changeType: 'positive' as const
    },
    {
      title: 'Average Ticket',
      value: `$${analytics.averageTicket.toFixed(2)}`,
      icon: TrendingUp,
      change: '+3.1%',
      changeType: 'positive' as const
    },
    {
      title: 'Daily Customers',
      value: '1,247',
      icon: Users,
      change: '+5.7%',
      changeType: 'positive' as const
    }
  ];

  return (
    <div className="space-y-6">
      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {kpiCards.map((kpi) => (
          <Card key={kpi.title}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{kpi.title}</p>
                  <p className="text-2xl font-bold">{kpi.value}</p>
                  <p className={`text-sm ${kpi.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
                    {kpi.change} from last period
                  </p>
                </div>
                <div className="p-3 bg-blue-50 rounded-lg">
                  <kpi.icon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales by Day Part */}
        <Card>
          <CardHeader>
            <CardTitle>Sales by Day Part</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={analytics.salesByDayPart}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day_part" />
                <YAxis />
                <Tooltip formatter={(value) => [`$${Number(value).toLocaleString()}`, 'Sales']} />
                <Bar dataKey="total_sales" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Sales Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Sales Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={analytics.salesTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip formatter={(value) => [`$${Number(value).toLocaleString()}`, 'Sales']} />
                <Line type="monotone" dataKey="sales" stroke="#3b82f6" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Top Menu Items */}
      <Card>
        <CardHeader>
          <CardTitle>Top Performing Menu Items</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.topMenuItems.map((item, index) => (
              <div key={item.item_name} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium">{item.item_name}</p>
                    <p className="text-sm text-gray-600">{item.quantity_sold} units sold</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold">${item.total_sales.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">Total Sales</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
