
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Brain, TrendingUp, AlertTriangle, Target, CheckCircle, Lightbulb } from 'lucide-react';

interface Insight {
  insight_key: number;
  title: string;
  description: string;
  insight_type: string;
  category: string;
  severity: string;
  confidence_score: number;
  recommended_actions?: string[];
}

interface AIInsightsListProps {
  insights?: Insight[];
  onResolveInsight: (insightKey: number) => void;
  isResolving: boolean;
}

export function AIInsightsList({ insights, onResolveInsight, isResolving }: AIInsightsListProps) {
  const getSeverityColor = (severity: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (severity) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'anomaly': return AlertTriangle;
      case 'prediction': return TrendingUp;
      case 'recommendation': return Lightbulb;
      case 'trend': return Target;
      default: return Brain;
    }
  };

  if (!insights || insights.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Insights</h3>
          <p className="text-gray-600">The AI system is analyzing your data and will provide insights soon.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {insights.map((insight) => {
        const IconComponent = getInsightIcon(insight.insight_type);
        return (
          <Card key={insight.insight_key} className="relative">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className="p-2 bg-blue-50 rounded-lg">
                    <IconComponent className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{insight.title}</CardTitle>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge variant={getSeverityColor(insight.severity)}>
                        {insight.severity}
                      </Badge>
                      <Badge variant="outline">{insight.category}</Badge>
                      <Badge variant="outline">{insight.insight_type}</Badge>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">
                    Confidence: {(insight.confidence_score * 100).toFixed(0)}%
                  </span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onResolveInsight(insight.insight_key)}
                    disabled={isResolving}
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Resolve
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">{insight.description}</p>
              
              {insight.recommended_actions && insight.recommended_actions.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Recommended Actions:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    {insight.recommended_actions.map((action, index) => (
                      <li key={index} className="text-sm text-gray-600">{action}</li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
