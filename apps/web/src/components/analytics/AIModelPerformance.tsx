
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, <PERSON>Tit<PERSON> } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON>, CheckCircle, AlertTriangle, TrendingUp } from 'lucide-react';

interface ModelMetrics {
  model_name: string;
  model_type: string;
  accuracy_score: number;
  precision_score: number;
  recall_score: number;
  f1_score: number;
  is_production_ready: boolean;
  training_date: string;
  deployment_date?: string;
}

export function AIModelPerformance() {
  // Mock data for demonstration - in real implementation, this would come from the database
  const models: ModelMetrics[] = [
    {
      model_name: 'Revenue Anomaly Detector v2.1',
      model_type: 'anomaly_detection',
      accuracy_score: 0.8456,
      precision_score: 0.7891,
      recall_score: 0.8123,
      f1_score: 0.8005,
      is_production_ready: true,
      training_date: '2024-12-01T10:00:00Z',
      deployment_date: '2024-12-02T14:30:00Z'
    },
    {
      model_name: 'Sales Forecasting Model v1.3',
      model_type: 'forecasting',
      accuracy_score: 0.9123,
      precision_score: 0.8967,
      recall_score: 0.9034,
      f1_score: 0.9000,
      is_production_ready: true,
      training_date: '2024-11-28T09:15:00Z',
      deployment_date: '2024-11-30T16:45:00Z'
    },
    {
      model_name: 'Customer Behavior Classifier v1.0',
      model_type: 'classification',
      accuracy_score: 0.7234,
      precision_score: 0.6891,
      recall_score: 0.7456,
      f1_score: 0.7165,
      is_production_ready: false,
      training_date: '2024-12-05T11:20:00Z'
    }
  ];

  const getModelTypeIcon = (type: string) => {
    switch (type) {
      case 'anomaly_detection': return AlertTriangle;
      case 'forecasting': return TrendingUp;
      case 'classification': return Brain;
      default: return Brain;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.85) return 'text-green-600';
    if (score >= 0.70) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center">
          <Brain className="h-6 w-6 mr-2 text-blue-600" />
          AI Model Performance
        </h2>
        <Badge variant="outline" className="text-sm">
          {models.filter(m => m.is_production_ready).length} Active Models
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {models.map((model, index) => {
          const IconComponent = getModelTypeIcon(model.model_type);
          
          return (
            <Card key={index}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="p-2 bg-blue-50 rounded-lg">
                      <IconComponent className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{model.model_name}</CardTitle>
                      <p className="text-sm text-gray-600 capitalize">
                        {model.model_type.replace('_', ' ')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {model.is_production_ready ? (
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Production
                      </Badge>
                    ) : (
                      <Badge variant="secondary">
                        Training
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium">Accuracy</span>
                      <span className={`text-sm font-bold ${getScoreColor(model.accuracy_score)}`}>
                        {(model.accuracy_score * 100).toFixed(1)}%
                      </span>
                    </div>
                    <Progress value={model.accuracy_score * 100} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium">Precision</span>
                      <span className={`text-sm font-bold ${getScoreColor(model.precision_score)}`}>
                        {(model.precision_score * 100).toFixed(1)}%
                      </span>
                    </div>
                    <Progress value={model.precision_score * 100} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium">Recall</span>
                      <span className={`text-sm font-bold ${getScoreColor(model.recall_score)}`}>
                        {(model.recall_score * 100).toFixed(1)}%
                      </span>
                    </div>
                    <Progress value={model.recall_score * 100} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium">F1 Score</span>
                      <span className={`text-sm font-bold ${getScoreColor(model.f1_score)}`}>
                        {(model.f1_score * 100).toFixed(1)}%
                      </span>
                    </div>
                    <Progress value={model.f1_score * 100} className="h-2" />
                  </div>
                </div>
                
                <div className="pt-3 border-t">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Trained:</span>
                      <p className="font-medium">
                        {new Date(model.training_date).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-600">Deployed:</span>
                      <p className="font-medium">
                        {model.deployment_date 
                          ? new Date(model.deployment_date).toLocaleDateString()
                          : 'Not deployed'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
