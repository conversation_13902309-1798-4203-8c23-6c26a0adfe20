
import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Brain } from 'lucide-react';

interface AIInsightsHeaderProps {
  selectedCategory: string;
  selectedSeverity: string;
  onCategoryChange: (value: string) => void;
  onSeverityChange: (value: string) => void;
}

export function AIInsightsHeader({
  selectedCategory,
  selectedSeverity,
  onCategoryChange,
  onSeverityChange
}: AIInsightsHeaderProps) {
  return (
    <div className="flex justify-between items-start">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 flex items-center">
          <Brain className="h-8 w-8 mr-3 text-blue-600" />
          AI-Powered Analytics
        </h1>
        <p className="text-gray-600 mt-2">Advanced insights, anomaly detection, and predictive analytics</p>
      </div>
      
      <div className="flex space-x-3">
        <Select value={selectedCategory} onValueChange={onCategoryChange}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="All Categories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="revenue">Revenue</SelectItem>
            <SelectItem value="operations">Operations</SelectItem>
            <SelectItem value="customer">Customer</SelectItem>
            <SelectItem value="inventory">Inventory</SelectItem>
            <SelectItem value="labor">Labor</SelectItem>
          </SelectContent>
        </Select>
        
        <Select value={selectedSeverity} onValueChange={onSeverityChange}>
          <SelectTrigger className="w-32">
            <SelectValue placeholder="All Severity" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Severity</SelectItem>
            <SelectItem value="critical">Critical</SelectItem>
            <SelectItem value="high">High</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="low">Low</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
