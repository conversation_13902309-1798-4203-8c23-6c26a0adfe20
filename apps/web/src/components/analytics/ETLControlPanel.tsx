
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useDataWarehouseSync } from '@/hooks/useAnalytics';
import { ETLService } from '@/services/etlService';
import { toast } from '@/hooks/use-toast';
import { RefreshCw, Database, Clock, CheckCircle, AlertCircle } from 'lucide-react';

export function ETLControlPanel() {
  const { data: syncStatus, refetch } = useDataWarehouseSync();
  const [isRunning, setIsRunning] = useState(false);

  const handleRunETL = async () => {
    setIsRunning(true);
    try {
      toast({
        title: "ETL Process Started",
        description: "Syncing operational data to analytics warehouse...",
      });

      const results = await ETLService.runFullETL();
      
      const hasErrors = Object.values(results).some(result => !result.success);
      
      toast({
        title: hasErrors ? "ETL Completed with Warnings" : "ETL Process Completed",
        description: hasErrors 
          ? "Some data synced successfully, check logs for details"
          : "All operational data synced to analytics warehouse",
        variant: hasErrors ? "destructive" : "default"
      });

      // Refresh sync status
      refetch();
    } catch (error) {
      toast({
        title: "ETL Process Failed",
        description: "Error occurred during data synchronization",
        variant: "destructive"
      });
    } finally {
      setIsRunning(false);
    }
  };

  const handleSyncSpecific = async (type: 'locations' | 'menuItems' | 'employees' | 'transactions') => {
    try {
      let result;
      switch (type) {
        case 'locations':
          result = await ETLService.syncLocations();
          break;
        case 'menuItems':
          result = await ETLService.syncMenuItems();
          break;
        case 'employees':
          result = await ETLService.syncEmployees();
          break;
        case 'transactions':
          result = await ETLService.syncSalesTransactions();
          break;
      }

      toast({
        title: result.success ? "Sync Completed" : "Sync Failed",
        description: result.success 
          ? `${result.recordsProcessed} records synced successfully`
          : result.error,
        variant: result.success ? "default" : "destructive"
      });

      if (result.success) {
        refetch();
      }
    } catch (error) {
      toast({
        title: "Sync Failed",
        description: "Error occurred during synchronization",
        variant: "destructive"
      });
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-5 w-5" />
            <span>Data Warehouse ETL Control</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Sync Status */}
            <div className="space-y-4">
              <h3 className="font-medium">Sync Status</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Last Sync:</span>
                  <span className="text-sm font-medium">
                    {formatDate(syncStatus?.lastSync)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Status:</span>
                  <div className="flex items-center space-x-1">
                    {syncStatus?.status === 'completed' ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-yellow-600" />
                    )}
                    <span className="text-sm font-medium capitalize">
                      {syncStatus?.status || 'Unknown'}
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Records Processed:</span>
                  <span className="text-sm font-medium">
                    {syncStatus?.recordsProcessed?.toLocaleString() || '0'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Next Sync:</span>
                  <span className="text-sm font-medium">
                    {formatDate(syncStatus?.nextSync)}
                  </span>
                </div>
              </div>
            </div>

            {/* Full ETL Control */}
            <div className="space-y-4">
              <h3 className="font-medium">Full ETL Process</h3>
              <Button
                onClick={handleRunETL}
                disabled={isRunning}
                className="w-full"
                size="lg"
              >
                {isRunning ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Running ETL...
                  </>
                ) : (
                  <>
                    <Database className="h-4 w-4 mr-2" />
                    Run Full ETL
                  </>
                )}
              </Button>
              <p className="text-xs text-gray-500">
                Syncs all operational data to the analytics warehouse
              </p>
            </div>

            {/* Scheduled Sync */}
            <div className="space-y-4">
              <h3 className="font-medium">Scheduled Sync</h3>
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">Every hour</span>
              </div>
              <Button variant="outline" size="sm" className="w-full">
                Configure Schedule
              </Button>
              <p className="text-xs text-gray-500">
                Automatic sync runs every hour to keep data current
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Individual Sync Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Individual Data Sync</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button
              variant="outline"
              onClick={() => handleSyncSpecific('locations')}
              className="flex flex-col items-center p-4 h-auto"
            >
              <Database className="h-6 w-6 mb-2" />
              <span>Locations</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => handleSyncSpecific('menuItems')}
              className="flex flex-col items-center p-4 h-auto"
            >
              <Database className="h-6 w-6 mb-2" />
              <span>Menu Items</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => handleSyncSpecific('employees')}
              className="flex flex-col items-center p-4 h-auto"
            >
              <Database className="h-6 w-6 mb-2" />
              <span>Employees</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => handleSyncSpecific('transactions')}
              className="flex flex-col items-center p-4 h-auto"
            >
              <Database className="h-6 w-6 mb-2" />
              <span>Transactions</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
