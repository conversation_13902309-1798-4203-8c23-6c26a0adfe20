
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AlertTriangle, TrendingDown, TrendingUp, Target, Users, DollarSign } from "lucide-react";

interface BusinessAlertsPanelProps {
  locationId?: string | null;
}

export const BusinessAlertsPanel = ({ locationId }: BusinessAlertsPanelProps) => {
  // Mock business alerts data - in real implementation, this would come from AI analysis
  const businessAlerts = [
    {
      id: 1,
      type: 'critical',
      category: 'Cost Management',
      title: 'Labor Cost Spike',
      description: locationId 
        ? 'Labor costs at this location have increased 15% this week due to overtime scheduling.'
        : 'Labor costs at Downtown location have increased 15% this week due to overtime scheduling.',
      impact: 'High',
      recommendation: 'Review scheduling optimization and consider additional part-time staff.',
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    },
    {
      id: 2,
      type: 'opportunity',
      category: 'Revenue Growth',
      title: 'Upselling Opportunity',
      description: locationId
        ? 'Weekend dessert sales are 40% below potential at this location based on customer demographics.'
        : 'Weekend dessert sales are 40% below potential based on customer demographics.',
      impact: 'Medium',
      recommendation: 'Train staff on dessert upselling techniques and consider promotional bundling.',
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      id: 3,
      type: 'warning',
      category: 'Customer Experience',
      title: 'Service Speed Decline',
      description: locationId
        ? 'Average ticket time has increased by 8 minutes during lunch rush at this location.'
        : 'Average ticket time has increased by 8 minutes during lunch rush at Mall location.',
      impact: 'Medium',
      recommendation: 'Analyze kitchen workflow and consider prep optimization during peak hours.',
      icon: Target,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200'
    },
    {
      id: 4,
      type: 'insight',
      category: 'Marketing',
      title: 'Customer Retention Trend',
      description: locationId
        ? 'Loyalty program engagement has increased 25% at this location following recent app updates.'
        : 'Loyalty program engagement has increased 25% following recent app updates.',
      impact: 'Positive',
      recommendation: 'Expand successful features to other customer touchpoints.',
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    }
  ];

  const getPriorityBadge = (type: string) => {
    switch (type) {
      case 'critical':
        return <Badge variant="destructive">Critical</Badge>;
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300">Warning</Badge>;
      case 'opportunity':
        return <Badge className="bg-green-100 text-green-800 border-green-300">Opportunity</Badge>;
      case 'insight':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-300">Insight</Badge>;
      default:
        return <Badge variant="secondary">Info</Badge>;
    }
  };

  return (
    <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-orange-600" />
            <CardTitle className="text-lg">
              AI Business Insights & Alerts
              {locationId && <span className="text-sm font-normal text-gray-600 ml-2">- Location Specific</span>}
            </CardTitle>
          </div>
          <Button variant="outline" size="sm" className="border-2 border-orange-200 hover:bg-orange-50">
            View All
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {businessAlerts.map((alert) => {
          const IconComponent = alert.icon;
          return (
            <Alert key={alert.id} className={`${alert.bgColor} ${alert.borderColor} border-2`}>
              <div className="flex items-start gap-3">
                <div className={`p-2 rounded-lg ${alert.bgColor} border-2 ${alert.borderColor}`}>
                  <IconComponent className={`h-4 w-4 ${alert.color}`} />
                </div>
                <div className="flex-1 space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <h4 className="font-semibold text-gray-900">{alert.title}</h4>
                      {getPriorityBadge(alert.type)}
                    </div>
                    <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded border">
                      {alert.category}
                    </span>
                  </div>
                  <AlertDescription className="text-gray-700">
                    {alert.description}
                  </AlertDescription>
                  <div className="bg-white p-3 rounded-lg border border-gray-200">
                    <p className="text-sm font-medium text-gray-600 mb-1">💡 Recommended Action:</p>
                    <p className="text-sm text-gray-700">{alert.recommendation}</p>
                  </div>
                  <div className="flex justify-between items-center pt-2">
                    <span className="text-xs text-gray-500">
                      Impact: <span className="font-medium">{alert.impact}</span>
                    </span>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" className="text-xs">
                        View Details
                      </Button>
                      <Button size="sm" className="text-xs bg-orange-500 hover:bg-orange-600">
                        Take Action
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </Alert>
          );
        })}
      </CardContent>
    </Card>
  );
};
