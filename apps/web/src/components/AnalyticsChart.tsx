
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { useSalesTransactions } from "@/hooks/useRestaurantData";
import { Skeleton } from "@/components/ui/skeleton";

export const AnalyticsChart = () => {
  const { data: transactions, isLoading } = useSalesTransactions(undefined, 30);

  // Process data for chart
  const chartData = transactions ? 
    transactions.reduce((acc: any[], transaction) => {
      const date = new Date(transaction.transaction_date).toLocaleDateString();
      const existingDay = acc.find(item => item.date === date);
      
      if (existingDay) {
        existingDay.revenue += Number(transaction.total_amount);
        existingDay.transactions += 1;
      } else {
        acc.push({
          date,
          revenue: Number(transaction.total_amount),
          transactions: 1
        });
      }
      
      return acc;
    }, []).slice(-7) // Last 7 days
    : [];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Revenue Trends</CardTitle>
        <CardDescription>
          Daily revenue and transaction volume for the last 7 days
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <Skeleton className="h-80 w-full" />
        ) : chartData.length === 0 ? (
          <div className="h-80 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <p className="text-lg font-medium">No data available</p>
              <p className="text-sm">Upload some sales data to see analytics</p>
            </div>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={60}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip 
                formatter={(value: number, name: string) => [
                  name === 'revenue' ? formatCurrency(value) : value,
                  name === 'revenue' ? 'Revenue' : 'Transactions'
                ]}
              />
              <Line 
                type="monotone" 
                dataKey="revenue" 
                stroke="#3b82f6" 
                strokeWidth={3}
                dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        )}
      </CardContent>
    </Card>
  );
};
