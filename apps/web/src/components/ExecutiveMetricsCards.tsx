
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { TrendingUp, TrendingDown, DollarSign, Users, Target, AlertTriangle } from "lucide-react";
import { useDashboardMetrics } from "@/hooks/useRestaurantData";
import { Skeleton } from "@/components/ui/skeleton";

export const ExecutiveMetricsCards = () => {
  const { data: metrics, isLoading, error } = useDashboardMetrics();

  if (error) {
    console.error('Executive metrics error:', error);
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  // Mock executive KPI data - in real implementation, this would come from the API
  const executiveKPIs = {
    sameStoreSalesGrowth: 8.2,
    avgTransactionValue: 24.50,
    foodCostPercentage: 28.5,
    laborCostPercentage: 32.1,
    customerRetentionRate: 85.7,
    ebitdaMargin: 15.3
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* Same-Store Sales Growth */}
      <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50 hover:shadow-lg hover:shadow-orange-100/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-700">Same-Store Sales Growth</CardTitle>
          <div className="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
            <TrendingUp className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-8 w-20" />
          ) : (
            <>
              <div className="text-2xl font-bold text-green-600">
                +{formatPercentage(executiveKPIs.sameStoreSalesGrowth)}
              </div>
              <p className="text-xs text-orange-600 font-medium">
                📈 vs. previous period
              </p>
            </>
          )}
        </CardContent>
      </Card>

      {/* Average Transaction Value */}
      <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50 hover:shadow-lg hover:shadow-orange-100/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-700">Avg Transaction Value</CardTitle>
          <div className="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
            <DollarSign className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-8 w-20" />
          ) : (
            <>
              <div className="text-2xl font-bold text-gray-900">
                {formatCurrency(executiveKPIs.avgTransactionValue)}
              </div>
              <p className="text-xs text-orange-600 font-medium">
                💰 {metrics?.transactionCount || 0} transactions today
              </p>
            </>
          )}
        </CardContent>
      </Card>

      {/* Food Cost Percentage */}
      <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50 hover:shadow-lg hover:shadow-orange-100/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-700">Food Cost %</CardTitle>
          <div className="p-2 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg">
            <Target className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-8 w-16" />
          ) : (
            <>
              <div className="text-2xl font-bold text-yellow-600">
                {formatPercentage(executiveKPIs.foodCostPercentage)}
              </div>
              <p className="text-xs text-orange-600 font-medium">
                🎯 Target: 30% or below
              </p>
            </>
          )}
        </CardContent>
      </Card>

      {/* Labor Cost Percentage */}
      <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50 hover:shadow-lg hover:shadow-orange-100/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-700">Labor Cost %</CardTitle>
          <div className="p-2 bg-gradient-to-r from-red-500 to-red-600 rounded-lg">
            <AlertTriangle className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-8 w-16" />
          ) : (
            <>
              <div className="text-2xl font-bold text-red-600">
                {formatPercentage(executiveKPIs.laborCostPercentage)}
              </div>
              <p className="text-xs text-orange-600 font-medium">
                ⚠️ Above target of 30%
              </p>
            </>
          )}
        </CardContent>
      </Card>

      {/* Customer Retention Rate */}
      <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50 hover:shadow-lg hover:shadow-orange-100/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-700">Customer Retention</CardTitle>
          <div className="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
            <Users className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-8 w-16" />
          ) : (
            <>
              <div className="text-2xl font-bold text-blue-600">
                {formatPercentage(executiveKPIs.customerRetentionRate)}
              </div>
              <p className="text-xs text-orange-600 font-medium">
                👥 Strong loyalty performance
              </p>
            </>
          )}
        </CardContent>
      </Card>

      {/* EBITDA Margin */}
      <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50 hover:shadow-lg hover:shadow-orange-100/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-700">EBITDA Margin</CardTitle>
          <div className="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
            <TrendingUp className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-8 w-16" />
          ) : (
            <>
              <div className="text-2xl font-bold text-green-600">
                {formatPercentage(executiveKPIs.ebitdaMargin)}
              </div>
              <p className="text-xs text-orange-600 font-medium">
                💼 Healthy profitability
              </p>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
