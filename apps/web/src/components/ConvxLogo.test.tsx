import { render } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { ConvxLogo } from './ConvxLogo'

describe('ConvxLogo', () => {
  it('renders with default props', () => {
    const { container } = render(<ConvxLogo />)
    const svg = container.querySelector('svg')
    
    expect(svg).toBeInTheDocument()
    expect(svg).toHaveAttribute('width', '44')
    expect(svg).toHaveAttribute('height', '44')
  })

  it('renders with custom dimensions', () => {
    const { container } = render(<ConvxLogo width={100} height={100} />)
    const svg = container.querySelector('svg')
    
    expect(svg).toHaveAttribute('width', '100')
    expect(svg).toHaveAttribute('height', '100')
  })

  it('applies custom className', () => {
    const { container } = render(<ConvxLogo className="custom-class" />)
    const svg = container.querySelector('svg')
    
    expect(svg).toHaveClass('custom-class')
  })
})
