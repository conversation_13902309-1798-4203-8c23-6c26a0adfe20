
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Search, Filter, MoreHorizontal, UserPlus } from "lucide-react";

const users = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    status: "Active",
    lastSeen: "2 minutes ago",
    initials: "<PERSON>",
    conversations: 45,
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "User",
    status: "Active",
    lastSeen: "1 hour ago",
    initials: "<PERSON><PERSON>",
    conversations: 23,
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Moderator",
    status: "Inactive",
    lastSeen: "2 days ago",
    initials: "<PERSON><PERSON>",
    conversations: 67,
  },
  {
    id: 4,
    name: "<PERSON>",
    email: "alex.rodrigue<PERSON>@example.com",
    role: "User",
    status: "Active",
    lastSeen: "5 minutes ago",
    initials: "AR",
    conversations: 12,
  },
  {
    id: 5,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "User",
    status: "Pending",
    lastSeen: "Never",
    initials: "JW",
    conversations: 0,
  },
];

const getStatusColor = (status: string) => {
  switch (status) {
    case "Active": return "bg-green-100 text-green-800";
    case "Inactive": return "bg-gray-100 text-gray-800";
    case "Pending": return "bg-yellow-100 text-yellow-800";
    default: return "bg-gray-100 text-gray-800";
  }
};

const getRoleColor = (role: string) => {
  switch (role) {
    case "Admin": return "bg-purple-100 text-purple-800";
    case "Moderator": return "bg-blue-100 text-blue-800";
    case "User": return "bg-gray-100 text-gray-800";
    default: return "bg-gray-100 text-gray-800";
  }
};

export function UserTable() {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold text-gray-900">
              User Management
            </CardTitle>
            <p className="text-sm text-gray-600">Manage users, roles, and permissions</p>
          </div>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white">
            <UserPlus className="w-4 h-4 mr-2" />
            Add User
          </Button>
        </div>
        
        <div className="flex items-center space-x-2 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-gray-50 border-gray-200"
            />
          </div>
          <Button variant="outline" className="border-gray-200">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-700">User</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Role</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Conversations</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Last Seen</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.map((user) => (
                <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                  <td className="py-4 px-4">
                    <div className="flex items-center space-x-3">
                      <Avatar className="w-10 h-10">
                        <AvatarFallback className="bg-blue-600 text-white text-sm font-medium">
                          {user.initials}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium text-gray-900">{user.name}</p>
                        <p className="text-sm text-gray-600">{user.email}</p>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <Badge className={`${getRoleColor(user.role)}`}>
                      {user.role}
                    </Badge>
                  </td>
                  <td className="py-4 px-4">
                    <Badge className={`${getStatusColor(user.status)}`}>
                      {user.status}
                    </Badge>
                  </td>
                  <td className="py-4 px-4">
                    <span className="font-medium text-gray-900">{user.conversations}</span>
                  </td>
                  <td className="py-4 px-4">
                    <span className="text-sm text-gray-600">{user.lastSeen}</span>
                  </td>
                  <td className="py-4 px-4">
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}
