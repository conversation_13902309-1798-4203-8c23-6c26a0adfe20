import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { IntegrationData } from "./IntegrationWizard";
import { PageWalkthrough } from "@/components/walkthrough/PageWalkthrough";
import { integrationStep4WalkthroughSteps } from "@/data/integrationStep4WalkthroughSteps";

interface DataMappingProps {
  integrationData: IntegrationData;
  onNext: (dataMapping: any) => void;
  onBack: () => void;
}
const dataTypes = [{
  id: "sales",
  name: "Sales Data",
  description: "Transaction data, revenue, payment methods",
  frequency: ["Real-time", "Every 15 minutes", "Hourly", "Daily"]
}, {
  id: "menu",
  name: "Menu Items",
  description: "Product catalog, pricing, categories",
  frequency: ["Real-time", "Daily", "Weekly"]
}, {
  id: "customers",
  name: "Customer Data",
  description: "Customer profiles, contact information, preferences",
  frequency: ["Real-time", "Hourly", "Daily"]
}, {
  id: "inventory",
  name: "Inventory",
  description: "Stock levels, ingredient costs, suppliers",
  frequency: ["Real-time", "Hourly", "Daily"]
}, {
  id: "staff",
  name: "Staff Data",
  description: "Employee schedules, performance, time tracking",
  frequency: ["Daily", "Weekly"]
}];
export const DataMapping = ({
  integrationData,
  onNext,
  onBack
}: DataMappingProps) => {
  const [selectedTypes, setSelectedTypes] = useState<Record<string, any>>({});
  const handleTypeToggle = (typeId: string, checked: boolean) => {
    if (checked) {
      setSelectedTypes(prev => ({
        ...prev,
        [typeId]: {
          enabled: true,
          frequency: dataTypes.find(t => t.id === typeId)?.frequency[0]
        }
      }));
    } else {
      setSelectedTypes(prev => {
        const newTypes = {
          ...prev
        };
        delete newTypes[typeId];
        return newTypes;
      });
    }
  };
  const handleFrequencyChange = (typeId: string, frequency: string) => {
    setSelectedTypes(prev => ({
      ...prev,
      [typeId]: {
        ...prev[typeId],
        frequency
      }
    }));
  };
  const handleNext = () => {
    onNext({
      selectedDataTypes: selectedTypes,
      syncSettings: {
        batchSize: 1000,
        retryAttempts: 3,
        errorHandling: "continue"
      }
    });
  };
  return (
    <>
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Select Data</h2>
          <p className="text-gray-600">Choose which data to sync and how often</p>
        </div>

        <div className="space-y-4" data-tour="data-selection">
          {dataTypes.map(type => (
            <Card key={type.id} className="border-2">
              <CardContent className="p-4">
                <div className="flex items-start space-x-3">
                  <Checkbox 
                    id={type.id} 
                    checked={!!selectedTypes[type.id]} 
                    onCheckedChange={(checked) => handleTypeToggle(type.id, !!checked)} 
                  />
                  <div className="flex-1">
                    <Label htmlFor={type.id} className="text-base font-semibold cursor-pointer">
                      {type.name}
                    </Label>
                    <p className="text-sm text-gray-600 mt-1">{type.description}</p>
                    
                    {selectedTypes[type.id] && (
                      <div className="mt-3" data-tour="sync-frequency">
                        <Label className="text-sm font-medium">Sync Frequency:</Label>
                        <select 
                          value={selectedTypes[type.id].frequency} 
                          onChange={(e) => handleFrequencyChange(type.id, e.target.value)} 
                          className="ml-2 px-2 py-1 border rounded text-sm"
                        >
                          {type.frequency.map(freq => (
                            <option key={freq} value={freq}>{freq}</option>
                          ))}
                        </select>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Card data-tour="sync-settings">
          <CardHeader>
            <CardTitle>Sync Settings</CardTitle>
            <CardDescription>Additional configuration options</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Batch Size</Label>
                <select className="w-full mt-1 px-3 py-2 border rounded">
                  <option value="500">500 records</option>
                  <option value="1000">1,000 records</option>
                  <option value="5000">5,000 records</option>
                </select>
              </div>
              <div>
                <Label>Error Handling</Label>
                <select className="w-full mt-1 px-3 py-2 border rounded">
                  <option value="continue">Continue on error</option>
                  <option value="retry">Retry failed records</option>
                  <option value="stop">Stop on first error</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-between">
          <Button variant="outline" onClick={onBack}>
            Back
          </Button>
          <Button onClick={handleNext} disabled={Object.keys(selectedTypes).length === 0}>
            Next: Review & Confirm
          </Button>
        </div>
      </div>

      <PageWalkthrough
        steps={integrationStep4WalkthroughSteps}
        pageKey="integration-step-4"
        autoStart={true}
        triggerClassName="fixed bottom-6 right-6 z-30"
      />
    </>
  );
};
