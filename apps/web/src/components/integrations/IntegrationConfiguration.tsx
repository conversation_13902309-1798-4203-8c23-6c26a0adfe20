
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { PageWalkthrough } from "@/components/walkthrough/PageWalkthrough";
import { integrationStep2WalkthroughSteps } from "@/data/integrationStep2WalkthroughSteps";

interface IntegrationConfigurationProps {
  integrationType?: string;
  onNext: (config: Record<string, any>) => void;
  onBack: () => void;
}

const getConfigFields = (type: string) => {
  const configs = {
    toast: [{
      name: "apiKey",
      label: "API Key",
      type: "password",
      required: true,
      placeholder: "Enter your Toast API key"
    }, {
      name: "locationId",
      label: "Location ID",
      type: "text",
      required: true,
      placeholder: "Restaurant location ID"
    }, {
      name: "environment",
      label: "Environment",
      type: "select",
      options: ["production", "sandbox"],
      required: true
    }],
    square: [{
      name: "accessToken",
      label: "Access Token",
      type: "password",
      required: true,
      placeholder: "Enter Square access token"
    }, {
      name: "applicationId",
      label: "Application ID",
      type: "text",
      required: true,
      placeholder: "Square application ID"
    }, {
      name: "locationId",
      label: "Location ID",
      type: "text",
      required: true,
      placeholder: "Square location ID"
    }],
    clover: [{
      name: "accessToken",
      label: "Access Token",
      type: "password",
      required: true,
      placeholder: "Enter Clover access token"
    }, {
      name: "merchantId",
      label: "Merchant ID",
      type: "text",
      required: true,
      placeholder: "Clover merchant ID"
    }],
    quickbooks: [{
      name: "clientId",
      label: "Client ID",
      type: "text",
      required: true,
      placeholder: "QuickBooks app client ID"
    }, {
      name: "clientSecret",
      label: "Client Secret",
      type: "password",
      required: true,
      placeholder: "QuickBooks app secret"
    }, {
      name: "companyId",
      label: "Company ID",
      type: "text",
      required: true,
      placeholder: "QuickBooks company ID"
    }],
    "google-analytics": [{
      name: "trackingId",
      label: "Tracking ID",
      type: "text",
      required: true,
      placeholder: "GA tracking ID (GA-XXXXXXXXX)"
    }, {
      name: "propertyId",
      label: "Property ID",
      type: "text",
      required: true,
      placeholder: "GA4 property ID"
    }],
    "custom-api": [{
      name: "baseUrl",
      label: "Base URL",
      type: "url",
      required: true,
      placeholder: "https://api.example.com"
    }, {
      name: "apiKey",
      label: "API Key",
      type: "password",
      required: false,
      placeholder: "API key (if required)"
    }, {
      name: "headers",
      label: "Custom Headers",
      type: "textarea",
      required: false,
      placeholder: "JSON format: {\"Authorization\": \"Bearer token\"}"
    }]
  };
  return configs[type as keyof typeof configs] || [];
};

export const IntegrationConfiguration = ({
  integrationType,
  onNext,
  onBack
}: IntegrationConfigurationProps) => {
  const [config, setConfig] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const fields = getConfigFields(integrationType || "");

  const handleInputChange = (name: string, value: string) => {
    setConfig(prev => ({
      ...prev,
      [name]: value
    }));
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    fields.forEach(field => {
      if (field.required && !config[field.name]) {
        newErrors[field.name] = `${field.label} is required`;
      }
    });
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      onNext(config);
    }
  };

  const renderField = (field: any) => {
    switch (field.type) {
      case "textarea":
        return <Textarea id={field.name} value={config[field.name] || ""} onChange={e => handleInputChange(field.name, e.target.value)} placeholder={field.placeholder} className={errors[field.name] ? "border-red-500" : ""} />;
      case "select":
        return <select id={field.name} value={config[field.name] || ""} onChange={e => handleInputChange(field.name, e.target.value)} className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ${errors[field.name] ? "border-red-500" : ""}`}>
            <option value="">Select {field.label}</option>
            {field.options?.map((option: string) => <option key={option} value={option}>{option}</option>)}
          </select>;
      default:
        return <Input id={field.name} type={field.type} value={config[field.name] || ""} onChange={e => handleInputChange(field.name, e.target.value)} placeholder={field.placeholder} className={errors[field.name] ? "border-red-500" : ""} />;
    }
  };

  return (
    <>
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Configure Data Mapping</h2>
          <p className="text-gray-600">Enter the connection details for your {integrationType} integration</p>
        </div>

        <Card data-tour="configuration-form">
          <CardHeader>
            <CardTitle>Connection Settings</CardTitle>
            <CardDescription>
              Provide the required information to establish a secure connection
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {fields.map(field => (
              <div key={field.name} className="space-y-2">
                <Label htmlFor={field.name}>
                  {field.label} {field.required && <span className="text-red-500">*</span>}
                </Label>
                {renderField(field)}
                {errors[field.name] && (
                  <p className="text-sm text-red-500">{errors[field.name]}</p>
                )}
              </div>
            ))}
          </CardContent>
        </Card>

        <div className="flex justify-between">
          <Button variant="outline" onClick={onBack}>
            Back
          </Button>
          <Button onClick={handleNext}>
            Next: Test Connection
          </Button>
        </div>
      </div>

      <PageWalkthrough
        steps={integrationStep2WalkthroughSteps}
        pageKey="integration-step-2"
        autoStart={true}
        triggerClassName="fixed bottom-6 right-6 z-30"
      />
    </>
  );
};
