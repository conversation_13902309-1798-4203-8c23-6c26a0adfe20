
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle } from "lucide-react";
import { IntegrationData } from "./IntegrationWizard";
import { useToast } from "@/hooks/use-toast";
import { PageWalkthrough } from "@/components/walkthrough/PageWalkthrough";
import { integrationStep5WalkthroughSteps } from "@/data/integrationStep5WalkthroughSteps";

interface IntegrationConfirmationProps {
  integrationData: IntegrationData;
  onComplete: () => void;
  onBack: () => void;
}

export const IntegrationConfirmation = ({ integrationData, onComplete, onBack }: IntegrationConfirmationProps) => {
  const { toast } = useToast();

  const handleActivate = () => {
    // Simulate activation process
    toast({
      title: "Integration Activated",
      description: "Your integration has been successfully set up and is now active.",
    });
    onComplete();
  };

  const selectedDataTypes = Object.entries(integrationData.dataMapping?.selectedDataTypes || {});

  return (
    <>
      <div className="space-y-6">
        <div className="text-center">
          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-2">Ready to Activate</h2>
          <p className="text-gray-600">Review your integration settings before activation</p>
        </div>

        <div className="grid gap-4" data-tour="integration-summary">
          <Card>
            <CardHeader>
              <CardTitle>Integration Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="font-medium">Type:</span>
                  <span className="capitalize">{integrationData.type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Status:</span>
                  <span className="text-green-600">Connection Verified</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Response Time:</span>
                  <span>{integrationData.testResults?.responseTime}ms</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Data Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {selectedDataTypes.map(([typeId, config]: [string, any]) => (
                  <div key={typeId} className="flex justify-between items-center">
                    <span className="font-medium capitalize">{typeId.replace('_', ' ')}</span>
                    <span className="text-sm text-gray-600">{config.frequency}</span>
                  </div>
                ))}
                {selectedDataTypes.length === 0 && (
                  <p className="text-gray-500 italic">No data types selected</p>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>What Happens Next?</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  Integration will be activated and start syncing data
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  You'll receive a confirmation email with setup details
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  Data will appear in your dashboard within 15 minutes
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  You can monitor sync status in the Integrations page
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-between" data-tour="activation-process">
          <Button variant="outline" onClick={onBack}>
            Back
          </Button>
          <Button onClick={handleActivate} size="lg">
            Activate Integration
          </Button>
        </div>
      </div>

      <PageWalkthrough
        steps={integrationStep5WalkthroughSteps}
        pageKey="integration-step-5"
        autoStart={true}
        triggerClassName="fixed bottom-6 right-6 z-30"
      />
    </>
  );
};
