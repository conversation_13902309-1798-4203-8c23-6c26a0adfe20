
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Upload, FileText, Download } from 'lucide-react';

interface DataUpload {
  id: string;
  fileName: string;
  uploadedAt: string;
  status: 'processing' | 'completed' | 'failed';
  records: number;
  size: string;
}

const mockUploads: DataUpload[] = [
  {
    id: '1',
    fileName: 'sales_data_q4_2024.csv',
    uploadedAt: '2 hours ago',
    status: 'completed',
    records: 15247,
    size: '2.4 MB'
  },
  {
    id: '2',
    fileName: 'menu_items_update.xlsx',
    uploadedAt: '1 day ago',
    status: 'completed',
    records: 456,
    size: '145 KB'
  },
  {
    id: '3',
    fileName: 'customer_feedback.csv',
    uploadedAt: '3 days ago',
    status: 'processing',
    records: 0,
    size: '890 KB'
  },
  {
    id: '4',
    fileName: 'inventory_audit.csv',
    uploadedAt: '1 week ago',
    status: 'failed',
    records: 0,
    size: '3.1 MB'
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed': return 'bg-green-100 text-green-800';
    case 'processing': return 'bg-yellow-100 text-yellow-800';
    case 'failed': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

export const DataUploadsTable = () => {
  return (
    <Card data-tour="data-uploads">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Recent Data Uploads</CardTitle>
        <Button data-tour="upload-data">
          <Upload className="w-4 h-4 mr-2" />
          Upload Data
        </Button>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left py-3 px-4">File Name</th>
                <th className="text-left py-3 px-4">Status</th>
                <th className="text-left py-3 px-4">Records</th>
                <th className="text-left py-3 px-4">Size</th>
                <th className="text-left py-3 px-4">Uploaded</th>
                <th className="text-left py-3 px-4">Actions</th>
              </tr>
            </thead>
            <tbody>
              {mockUploads.map((upload) => (
                <tr key={upload.id} className="border-b hover:bg-gray-50">
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-2">
                      <FileText className="w-4 h-4 text-gray-500" />
                      <span className="font-medium">{upload.fileName}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <Badge variant="secondary" className={getStatusColor(upload.status)}>
                      {upload.status}
                    </Badge>
                  </td>
                  <td className="py-3 px-4 text-gray-600">
                    {upload.records > 0 ? upload.records.toLocaleString() : '-'}
                  </td>
                  <td className="py-3 px-4 text-gray-600">{upload.size}</td>
                  <td className="py-3 px-4 text-gray-600">{upload.uploadedAt}</td>
                  <td className="py-3 px-4">
                    <Button variant="ghost" size="sm">
                      <Download className="w-4 h-4" />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};
