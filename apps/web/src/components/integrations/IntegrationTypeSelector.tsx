
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { PageWalkthrough } from "@/components/walkthrough/PageWalkthrough";
import { integrationStep1WalkthroughSteps } from "@/data/integrationStep1WalkthroughSteps";

interface IntegrationTypeProps {
  onSelect: (type: string) => void;
}

const integrationTypes = [
  {
    id: "toast",
    name: "Toast POS",
    category: "POS Systems",
    description: "Connect your Toast point-of-sale system for real-time sales data",
    features: ["Sales Data", "Menu Items", "Customer Data"],
    logo: "🍞"
  },
  {
    id: "square",
    name: "Square",
    category: "POS Systems", 
    description: "Integrate with Square for payment and transaction data",
    features: ["Payments", "Inventory", "Customer Data"],
    logo: "⬜"
  },
  {
    id: "clover",
    name: "Clover",
    category: "POS Systems",
    description: "Connect Clover for comprehensive restaurant data",
    features: ["Sales", "Inventory", "Staff Management"],
    logo: "🍀"
  },
  {
    id: "par",
    name: "PA<PERSON>",
    category: "POS Systems",
    description: "Integrate with PAR POS for enterprise restaurant management",
    features: ["Sales Analytics", "Labor Management", "Inventory Control"],
    logo: "📊"
  },
  {
    id: "quickbooks",
    name: "QuickBooks",
    category: "Accounting",
    description: "Sync financial data from QuickBooks",
    features: ["Financial Reports", "Expenses", "Tax Data"],
    logo: "📊"
  },
  {
    id: "google-analytics",
    name: "Google Analytics",
    category: "Analytics",
    description: "Track website and online ordering analytics",
    features: ["Web Traffic", "Conversion Data", "Customer Journey"],
    logo: "📈"
  },
  {
    id: "spendgo",
    name: "SpendGo",
    category: "Loyalty & Marketing",
    description: "Connect SpendGo for customer loyalty and rewards data",
    features: ["Customer Loyalty", "Rewards Programs", "Marketing Analytics"],
    logo: "🎁"
  },
  {
    id: "opus",
    name: "Opus",
    category: "Loyalty & Marketing",
    description: "Integrate Opus for customer engagement and marketing automation",
    features: ["Email Marketing", "Customer Segmentation", "Campaign Analytics"],
    logo: "💌"
  },
  {
    id: "olo",
    name: "Olo",
    category: "Online Ordering",
    description: "Connect Olo for online ordering and delivery management",
    features: ["Online Orders", "Delivery Data", "Customer Preferences"],
    logo: "🛒"
  },
  {
    id: "uber",
    name: "Uber Eats",
    category: "Delivery Platforms",
    description: "Sync Uber Eats orders and delivery performance data",
    features: ["Delivery Orders", "Performance Metrics", "Customer Reviews"],
    logo: "🚗"
  },
  {
    id: "grubhub",
    name: "Grubhub",
    category: "Delivery Platforms",
    description: "Integrate with Grubhub for order and sales analytics",
    features: ["Order Data", "Commission Tracking", "Menu Performance"],
    logo: "🍔"
  },
  {
    id: "postmates",
    name: "Postmates",
    category: "Delivery Platforms",
    description: "Connect Postmates for delivery order management",
    features: ["Delivery Orders", "Driver Performance", "Order Tracking"],
    logo: "📦"
  },
  {
    id: "facebook",
    name: "Facebook",
    category: "Social Media",
    description: "Connect Facebook for social media analytics and marketing insights",
    features: ["Page Analytics", "Post Performance", "Audience Insights"],
    logo: "📘"
  },
  {
    id: "instagram",
    name: "Instagram",
    category: "Social Media",
    description: "Integrate Instagram for visual content performance and engagement data",
    features: ["Post Analytics", "Story Metrics", "Follower Growth"],
    logo: "📸"
  },
  {
    id: "twitter",
    name: "Twitter",
    category: "Social Media",
    description: "Connect Twitter for brand monitoring and engagement tracking",
    features: ["Tweet Analytics", "Mention Tracking", "Engagement Metrics"],
    logo: "🐦"
  },
  {
    id: "tiktok",
    name: "TikTok",
    category: "Social Media",
    description: "Integrate TikTok for video content performance and trending analysis",
    features: ["Video Analytics", "Trend Tracking", "Audience Demographics"],
    logo: "🎵"
  },
  {
    id: "youtube",
    name: "YouTube",
    category: "Social Media",
    description: "Connect YouTube for video marketing analytics and channel performance",
    features: ["Video Performance", "Subscriber Growth", "Watch Time Analytics"],
    logo: "📺"
  },
  {
    id: "linkedin",
    name: "LinkedIn",
    category: "Social Media",
    description: "Integrate LinkedIn for professional networking and business content analytics",
    features: ["Professional Analytics", "Company Page Insights", "Network Growth"],
    logo: "💼"
  },
  {
    id: "custom-api",
    name: "Custom API",
    category: "Custom",
    description: "Connect to any REST API endpoint",
    features: ["Custom Endpoints", "Flexible Data", "Webhooks"],
    logo: "⚙️"
  }
];

export const IntegrationTypeSelector = ({ onSelect }: IntegrationTypeProps) => {
  const groupedIntegrations = integrationTypes.reduce((acc, integration) => {
    if (!acc[integration.category]) {
      acc[integration.category] = [];
    }
    acc[integration.category].push(integration);
    return acc;
  }, {} as Record<string, typeof integrationTypes>);

  return (
    <>
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Choose Integration Type</h2>
          <p className="text-gray-600">Select the system you want to connect to your data platform</p>
        </div>

        <div data-tour="integration-categories">
          {Object.entries(groupedIntegrations).map(([category, integrations]) => (
            <div key={category}>
              <h3 className="text-lg font-semibold mb-3 text-gray-800">{category}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {integrations.map((integration) => (
                  <Card 
                    key={integration.id}
                    className="cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-blue-300"
                    onClick={() => onSelect(integration.id)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{integration.logo}</div>
                        <div>
                          <CardTitle className="text-lg">{integration.name}</CardTitle>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="mb-3">
                        {integration.description}
                      </CardDescription>
                      <div className="flex flex-wrap gap-1" data-tour="integration-features">
                        {integration.features.map((feature) => (
                          <span 
                            key={feature}
                            className="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      <PageWalkthrough
        steps={integrationStep1WalkthroughSteps}
        pageKey="integration-step-1"
        autoStart={true}
        triggerClassName="fixed bottom-6 right-6 z-30"
      />
    </>
  );
};
