
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Settings, Download, RotateCcw } from "lucide-react";

export function AlertsHeader() {
  return (
    <div className="flex flex-col lg:flex-row lg:items-center justify-between space-y-4 lg:space-y-0">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Alerts & Notifications</h1>
        <p className="text-gray-600">Monitor and manage your data platform alerts</p>
        <div className="flex items-center space-x-4 mt-2">
          <Badge className="bg-red-100 text-red-800">5 Critical</Badge>
          <Badge className="bg-yellow-100 text-yellow-800">12 Warning</Badge>
          <Badge className="bg-blue-100 text-blue-800">8 Info</Badge>
        </div>
      </div>
      
      <div className="flex items-center space-x-3">
        <Button variant="outline" size="sm">
          <Download className="w-4 h-4 mr-2" />
          Export
        </Button>
        <Button variant="outline" size="sm">
          <RotateCcw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
        <Button variant="outline" size="sm">
          <Settings className="w-4 h-4 mr-2" />
          Configure
        </Button>
      </div>
    </div>
  );
}
