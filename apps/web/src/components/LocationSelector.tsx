
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MapPin, Building2 } from "lucide-react";
import { useRestaurantLocations } from "@/hooks/useRestaurantData";
import { Skeleton } from "@/components/ui/skeleton";

interface LocationSelectorProps {
  selectedLocationId: string | null;
  onLocationChange: (locationId: string | null) => void;
}

export const LocationSelector = ({ selectedLocationId, onLocationChange }: LocationSelectorProps) => {
  const { data: locations, isLoading, error } = useRestaurantLocations();

  console.log('LocationSelector - locations data:', locations);
  console.log('LocationSelector - isLoading:', isLoading);
  console.log('LocationSelector - error:', error);

  if (isLoading) {
    return (
      <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5 text-orange-600" />
            Location View
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-10 w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    console.error('Error loading locations:', error);
    return (
      <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5 text-orange-600" />
            Location View
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-red-600">
            Error loading locations: {error.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  const selectedLocation = locations?.find(loc => loc.id === selectedLocationId);

  return (
    <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="w-5 h-5 text-orange-600" />
          Location View
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Select value={selectedLocationId || "all"} onValueChange={(value) => onLocationChange(value === "all" ? null : value)}>
          <SelectTrigger className="w-full border-2 border-orange-200">
            <SelectValue placeholder="Select a location" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">
              <div className="flex items-center gap-2">
                <Building2 className="w-4 h-4" />
                All Locations (Rollup View)
              </div>
            </SelectItem>
            {locations && locations.length > 0 ? (
              locations.map((location) => (
                <SelectItem key={location.id} value={location.id}>
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    {location.name}
                    {location.city && (
                      <span className="text-sm text-gray-500">({location.city})</span>
                    )}
                  </div>
                </SelectItem>
              ))
            ) : (
              <SelectItem value="no-locations" disabled>
                <div className="text-gray-500">No locations found</div>
              </SelectItem>
            )}
          </SelectContent>
        </Select>
        
        {selectedLocation && (
          <div className="mt-3 p-3 bg-white rounded-lg border border-orange-200">
            <div className="text-sm">
              <div className="font-medium text-gray-900">{selectedLocation.name}</div>
              {selectedLocation.address && (
                <div className="text-gray-600">
                  {selectedLocation.address}
                  {selectedLocation.city && `, ${selectedLocation.city}`}
                  {selectedLocation.state && `, ${selectedLocation.state}`}
                  {selectedLocation.zip_code && ` ${selectedLocation.zip_code}`}
                </div>
              )}
              {selectedLocation.manager_name && (
                <div className="text-gray-600">Manager: {selectedLocation.manager_name}</div>
              )}
            </div>
          </div>
        )}

        {locations && locations.length === 0 && (
          <div className="mt-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="text-sm text-yellow-800">
              No restaurant locations found in the system. You can add locations through the data integration or master data management sections.
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
