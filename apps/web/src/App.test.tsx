import { render } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import App from './App'

// Mock the router and providers
vi.mock('react-router-dom', () => ({
  BrowserRouter: ({ children }: { children: React.ReactNode }) => <div data-testid="app-router">{children}</div>,
  Routes: ({ children }: { children: React.ReactNode }) => <div data-testid="app-routes">{children}</div>,
  Route: () => <div data-testid="app-route">Route</div>,
}))

vi.mock('@/hooks/useAuth', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div data-testid="auth-provider">{children}</div>,
}))

vi.mock('@/contexts/OnboardingContext', () => ({
  OnboardingProvider: ({ children }: { children: React.ReactNode }) => <div data-testid="onboarding-provider">{children}</div>,
}))

describe('App', () => {
  it('renders without crashing', () => {
    const { getByTestId } = render(<App />)
    expect(getByTestId('app-router')).toBeInTheDocument()
    expect(getByTestId('auth-provider')).toBeInTheDocument()
    expect(getByTestId('onboarding-provider')).toBeInTheDocument()
  })
})
