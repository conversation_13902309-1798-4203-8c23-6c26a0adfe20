
export interface AIInsight {
  insight_key: number;
  insight_id: string;
  insight_type: 'anomaly' | 'prediction' | 'recommendation' | 'trend';
  category: 'revenue' | 'operations' | 'customer' | 'inventory' | 'labor';
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  confidence_score: number;
  impact_score: number;
  location_key?: number;
  insight_data: any;
  recommended_actions: string[];
  status: 'active' | 'resolved' | 'dismissed';
  created_date: string;
}

export interface AnomalyDetection {
  anomaly_key: number;
  anomaly_id: string;
  location_key?: number;
  metric_name: string;
  expected_value: number;
  actual_value: number;
  deviation_percentage: number;
  anomaly_score: number;
  detection_method: string;
  threshold_breached: 'upper' | 'lower' | 'both';
  business_impact: string;
  is_confirmed: boolean;
  created_date: string;
}

export interface PredictiveAnalytics {
  prediction_key: number;
  prediction_id: string;
  location_key?: number;
  prediction_type: 'sales_forecast' | 'demand_forecast' | 'labor_optimization';
  target_metric: string;
  predicted_value: number;
  confidence_level: number;
  forecast_date: string;
  model_name: string;
  created_date: string;
}

export interface BIMetrics {
  metric_key: number;
  metric_category: 'financial' | 'operational' | 'customer' | 'strategic';
  metric_name: string;
  metric_value: number;
  metric_unit: string;
  benchmark_value?: number;
  industry_benchmark?: number;
  trend_direction: 'up' | 'down' | 'stable';
  trend_strength: number;
}

export interface AIInsightsFilters {
  type?: string;
  category?: string;
  severity?: string;
  locationKey?: number;
}
