
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';

export interface TeamInvitation {
  id: string;
  email: string;
  role: 'CEO' | 'COO' | 'Data Admin' | 'Business Admin';
  status: 'pending' | 'accepted' | 'expired';
  invitedBy: string | null;
  expiresAt: Date;
  acceptedAt: Date | null;
  createdAt: Date;
}

export function useTeamInvitations() {
  const { user } = useAuth();
  const [invitations, setInvitations] = useState<TeamInvitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchInvitations();
    }
  }, [user]);

  const fetchInvitations = async () => {
    try {
      setLoading(true);
      
      // Get user's company_id first
      const { data: profile } = await supabase
        .from('profiles')
        .select('company_id')
        .eq('id', user?.id)
        .single();

      if (profile?.company_id) {
        const { data, error } = await supabase
          .from('team_invitations')
          .select('*')
          .eq('company_id', profile.company_id)
          .order('created_at', { ascending: false });

        if (error) throw error;

        setInvitations(data?.map(inv => ({
          id: inv.id,
          email: inv.email,
          role: inv.role as TeamInvitation['role'],
          status: inv.status as TeamInvitation['status'],
          invitedBy: inv.invited_by,
          expiresAt: new Date(inv.expires_at),
          acceptedAt: inv.accepted_at ? new Date(inv.accepted_at) : null,
          createdAt: new Date(inv.created_at)
        })) || []);
      }
    } catch (err) {
      console.error('Error fetching invitations:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch invitations');
    } finally {
      setLoading(false);
    }
  };

  const sendInvitation = async (email: string, role: TeamInvitation['role']) => {
    try {
      if (!user) throw new Error('User not authenticated');

      // Get user's company_id
      const { data: profile } = await supabase
        .from('profiles')
        .select('company_id')
        .eq('id', user.id)
        .single();

      if (!profile?.company_id) throw new Error('User not associated with a company');

      const { data, error } = await supabase
        .from('team_invitations')
        .insert({
          company_id: profile.company_id,
          email,
          role,
          invited_by: user.id
        })
        .select()
        .single();

      if (error) throw error;

      await fetchInvitations();
      return data;
    } catch (err) {
      console.error('Error sending invitation:', err);
      throw err;
    }
  };

  const resendInvitation = async (invitationId: string) => {
    try {
      const { error } = await supabase
        .from('team_invitations')
        .update({
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'pending'
        })
        .eq('id', invitationId);

      if (error) throw error;
      await fetchInvitations();
    } catch (err) {
      console.error('Error resending invitation:', err);
      throw err;
    }
  };

  const cancelInvitation = async (invitationId: string) => {
    try {
      const { error } = await supabase
        .from('team_invitations')
        .delete()
        .eq('id', invitationId);

      if (error) throw error;
      await fetchInvitations();
    } catch (err) {
      console.error('Error canceling invitation:', err);
      throw err;
    }
  };

  return {
    invitations,
    loading,
    error,
    sendInvitation,
    resendInvitation,
    cancelInvitation,
    refetch: fetchInvitations
  };
}
