
import { useState, useEffect } from 'react';

export function useWalkthrough() {
  const [showWalkthrough, setShowWalkthrough] = useState(false);
  const [hasSeenWalkthrough, setHasSeenWalkthrough] = useState(false);

  useEffect(() => {
    // Check if user has seen the walkthrough before
    const walkthroughCompleted = localStorage.getItem('walkthrough-completed');
    const welcomeCardDismissed = localStorage.getItem('welcome-card-dismissed');
    
    if (!walkthroughCompleted && !welcomeCardDismissed) {
      // Show walkthrough for new users after a brief delay
      const timer = setTimeout(() => {
        setShowWalkthrough(true);
      }, 2000);
      
      return () => clearTimeout(timer);
    } else {
      setHasSeenWalkthrough(true);
    }
  }, []);

  const completeWalkthrough = () => {
    localStorage.setItem('walkthrough-completed', 'true');
    setShowWalkthrough(false);
    setHasSeenWalkthrough(true);
  };

  const skipWalkthrough = () => {
    localStorage.setItem('walkthrough-completed', 'true');
    setShowWalkthrough(false);
    setHasSeenWalkthrough(true);
  };

  const startWalkthrough = () => {
    setShowWalkthrough(true);
  };

  return {
    showWalkthrough,
    hasSeenWalkthrough,
    completeWalkthrough,
    skipWalkthrough,
    startWalkthrough
  };
}
