
import { useState, useEffect } from 'react';
import { WalkthroughStep } from '@/types/walkthrough';

interface UsePageWalkthroughOptions {
  steps: WalkthroughStep[];
  pageKey: string;
  autoStart?: boolean;
  startDelay?: number;
}

export function usePageWalkthrough({ 
  steps, 
  pageKey, 
  autoStart = true, 
  startDelay = 2000 
}: UsePageWalkthroughOptions) {
  const [showWalkthrough, setShowWalkthrough] = useState(false);
  const [hasSeenWalkthrough, setHasSeenWalkthrough] = useState(false);

  const storageKey = `${pageKey}-walkthrough-completed`;

  useEffect(() => {
    // Check if user has seen this page's walkthrough before
    const walkthroughCompleted = localStorage.getItem(storageKey);
    
    if (!walkthroughCompleted && autoStart) {
      // Show walkthrough for new users after a brief delay
      const timer = setTimeout(() => {
        setShowWalkthrough(true);
      }, startDelay);
      
      return () => clearTimeout(timer);
    } else {
      setHasSeenWalkthrough(true);
    }
  }, [storageKey, autoStart, startDelay]);

  const completeWalkthrough = () => {
    localStorage.setItem(storageKey, 'true');
    setShowWalkthrough(false);
    setHasSeenWalkthrough(true);
  };

  const skipWalkthrough = () => {
    localStorage.setItem(storageKey, 'true');
    setShowWalkthrough(false);
    setHasSeenWalkthrough(true);
  };

  const startWalkthrough = () => {
    setShowWalkthrough(true);
  };

  return {
    showWalkthrough,
    hasSeenWalkthrough,
    steps,
    completeWalkthrough,
    skipWalkthrough,
    startWalkthrough
  };
}
