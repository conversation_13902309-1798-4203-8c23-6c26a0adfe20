
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface RestaurantLocation {
  id: string;
  name: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  phone?: string;
  manager_name?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface SalesTransaction {
  id: string;
  location_id?: string;
  transaction_date: string;
  total_amount: number;
  tax_amount?: number;
  tip_amount?: number;
  payment_method?: string;
  order_type?: string;
  customer_count?: number;
  pos_transaction_id?: string;
  source_system?: string;
  created_at: string;
  updated_at: string;
}

export interface DataIngestionLog {
  id: string;
  source_type: string;
  source_name: string;
  status: string;
  records_processed?: number;
  records_failed?: number;
  error_details?: any;
  started_at: string;
  completed_at?: string;
  created_by?: string;
  location_id?: string;
}

export const useRestaurantLocations = () => {
  return useQuery({
    queryKey: ['restaurant-locations'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('restaurant_locations')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as RestaurantLocation[];
    },
  });
};

export const useSalesTransactions = (locationId?: string, limit = 1000) => {
  return useQuery({
    queryKey: ['sales-transactions', locationId, limit],
    queryFn: async () => {
      let query = supabase
        .from('sales_transactions')
        .select('*')
        .order('transaction_date', { ascending: false })
        .limit(limit);

      if (locationId) {
        query = query.eq('location_id', locationId);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as SalesTransaction[];
    },
  });
};

export const useDataIngestionLogs = () => {
  return useQuery({
    queryKey: ['data-ingestion-logs'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('data_ingestion_logs')
        .select('*')
        .order('started_at', { ascending: false })
        .limit(50);

      if (error) throw error;
      return data as DataIngestionLog[];
    },
  });
};

export const useCreateSalesTransactions = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (transactions: Omit<SalesTransaction, 'id' | 'created_at' | 'updated_at'>[]) => {
      const { data, error } = await supabase
        .from('sales_transactions')
        .insert(transactions)
        .select();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sales-transactions'] });
    },
  });
};

export const useCreateDataIngestionLog = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (log: Omit<DataIngestionLog, 'id' | 'started_at'>) => {
      const { data, error } = await supabase
        .from('data_ingestion_logs')
        .insert(log)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['data-ingestion-logs'] });
    },
  });
};

export const useUpdateDataIngestionLog = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<DataIngestionLog> }) => {
      const { data, error } = await supabase
        .from('data_ingestion_logs')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['data-ingestion-logs'] });
    },
  });
};

// Dashboard metrics hook
export const useDashboardMetrics = () => {
  return useQuery({
    queryKey: ['dashboard-metrics'],
    queryFn: async () => {
      console.log('Fetching dashboard metrics...');
      
      // Get total sales for today
      const today = new Date().toISOString().split('T')[0];
      const { data: todaySales, error: salesError } = await supabase
        .from('sales_transactions')
        .select('total_amount')
        .gte('transaction_date', today);

      if (salesError) {
        console.error('Error fetching sales:', salesError);
        throw salesError;
      }

      // Get active locations count
      const { count: locationsCount, error: locationsError } = await supabase
        .from('restaurant_locations')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'active');

      if (locationsError) {
        console.error('Error fetching locations:', locationsError);
        throw locationsError;
      }

      // Get recent data ingestion status
      const { data: recentIngestions, error: ingestionError } = await supabase
        .from('data_ingestion_logs')
        .select('status')
        .order('started_at', { ascending: false })
        .limit(10);

      if (ingestionError) {
        console.error('Error fetching ingestions:', ingestionError);
        throw ingestionError;
      }

      const totalRevenue = todaySales?.reduce((sum, transaction) => sum + Number(transaction.total_amount), 0) || 0;
      const activeIntegrations = locationsCount || 0;
      const dataQuality = recentIngestions?.length > 0 
        ? (recentIngestions.filter(log => log.status === 'completed').length / recentIngestions.length) * 100 
        : 0;

      console.log('Dashboard metrics:', { totalRevenue, activeIntegrations, dataQuality });

      return {
        totalRevenue,
        activeIntegrations,
        dataQuality: Math.round(dataQuality),
        transactionCount: todaySales?.length || 0
      };
    },
  });
};
