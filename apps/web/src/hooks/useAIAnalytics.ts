
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AIAnalyticsService } from '@/services/ai/aiAnalyticsService';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';

export const useAIInsights = (tenantKey: number = 1, filters?: {
  type?: string;
  category?: string;
  severity?: string;
  locationKey?: number;
}) => {
  const { user } = useAuth();
  
  return useQuery({
    queryKey: ['ai-insights', tenantKey, filters],
    queryFn: () => AIAnalyticsService.getAIInsights(tenantKey, filters),
    enabled: !!user, // Only run when user is authenticated
    refetchInterval: 300000, // Refetch every 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on RLS policy violations
      if (error?.message?.includes('row-level security') || 
          error?.message?.includes('insufficient privilege') ||
          error?.message?.includes('permission denied')) {
        console.warn('Access denied to AI insights data - user may not have proper permissions');
        return false;
      }
      return failureCount < 3;
    },
    meta: {
      onError: (error: any) => {
        if (error?.message?.includes('row-level security') || 
            error?.message?.includes('insufficient privilege')) {
          console.log('User does not have access to AI insights for this tenant');
        }
      }
    }
  });
};

export const useAnomalyDetections = (tenantKey: number = 1, locationKey?: number) => {
  const { user } = useAuth();
  
  return useQuery({
    queryKey: ['anomaly-detections', tenantKey, locationKey],
    queryFn: () => AIAnalyticsService.getAnomalyDetections(tenantKey, locationKey),
    enabled: !!user,
    refetchInterval: 180000, // Refetch every 3 minutes
    retry: (failureCount, error) => {
      if (error?.message?.includes('row-level security') || 
          error?.message?.includes('insufficient privilege') ||
          error?.message?.includes('permission denied')) {
        console.warn('Access denied to anomaly detection data - user may not have proper permissions');
        return false;
      }
      return failureCount < 3;
    },
    meta: {
      onError: (error: any) => {
        if (error?.message?.includes('row-level security') || 
            error?.message?.includes('insufficient privilege')) {
          console.log('User does not have access to anomaly detection for this tenant');
        }
      }
    }
  });
};

export const usePredictiveAnalytics = (tenantKey: number = 1, predictionType?: string) => {
  const { user } = useAuth();
  
  return useQuery({
    queryKey: ['predictive-analytics', tenantKey, predictionType],
    queryFn: () => AIAnalyticsService.getPredictiveAnalytics(tenantKey, predictionType),
    enabled: !!user,
    refetchInterval: 600000, // Refetch every 10 minutes
    retry: (failureCount, error) => {
      if (error?.message?.includes('row-level security') || 
          error?.message?.includes('insufficient privilege') ||
          error?.message?.includes('permission denied')) {
        console.warn('Access denied to predictive analytics data - user may not have proper permissions');
        return false;
      }
      return failureCount < 3;
    },
    meta: {
      onError: (error: any) => {
        if (error?.message?.includes('row-level security') || 
            error?.message?.includes('insufficient privilege')) {
          console.log('User does not have access to predictive analytics for this tenant');
        }
      }
    }
  });
};

export const useBIMetrics = (tenantKey: number = 1, category?: string) => {
  const { user } = useAuth();
  
  return useQuery({
    queryKey: ['bi-metrics', tenantKey, category],
    queryFn: () => AIAnalyticsService.getBIMetrics(tenantKey, category),
    enabled: !!user,
    refetchInterval: 300000, // Refetch every 5 minutes
    retry: (failureCount, error) => {
      if (error?.message?.includes('row-level security') || 
          error?.message?.includes('insufficient privilege') ||
          error?.message?.includes('permission denied')) {
        console.warn('Access denied to BI metrics data - user may not have proper permissions');
        return false;
      }
      return failureCount < 3;
    },
    meta: {
      onError: (error: any) => {
        if (error?.message?.includes('row-level security') || 
            error?.message?.includes('insufficient privilege')) {
          console.log('User does not have access to BI metrics for this tenant');
        }
      }
    }
  });
};

export const useResolveInsight = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ insightKey, userId }: { insightKey: number; userId?: string }) =>
      AIAnalyticsService.markInsightAsResolved(insightKey, userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['ai-insights'] });
      toast({
        title: "Insight Resolved",
        description: "The insight has been marked as resolved successfully.",
      });
    },
    onError: (error: any) => {
      const errorMessage = error?.message?.includes('row-level security') || 
                          error?.message?.includes('insufficient privilege') ||
                          error?.message?.includes('permission denied')
        ? "You don't have permission to resolve this insight."
        : "Failed to resolve the insight. Please try again.";
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });
};

export const useConfirmAnomaly = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ anomalyKey, userId }: { anomalyKey: number; userId?: string }) =>
      AIAnalyticsService.confirmAnomaly(anomalyKey, userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['anomaly-detections'] });
      toast({
        title: "Anomaly Confirmed",
        description: "The anomaly has been confirmed and logged.",
      });
    },
    onError: (error: any) => {
      const errorMessage = error?.message?.includes('row-level security') || 
                          error?.message?.includes('insufficient privilege') ||
                          error?.message?.includes('permission denied')
        ? "You don't have permission to confirm this anomaly."
        : "Failed to confirm the anomaly. Please try again.";
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });
};
