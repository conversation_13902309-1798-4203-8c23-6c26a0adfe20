
# QA Testing Document - Alerts System

## Overview
This document outlines the testing procedures for the newly implemented alerts system in the CONVX Data Kitchen platform. The alerts system monitors data quality, sync health, and system performance to provide real-time notifications to users.

## System Architecture
- **Frontend**: React components with TypeScript
- **Backend**: Supabase integration with PostgreSQL
- **Data Sources**: `data_ingestion_logs` and `integration_status` tables
- **Alert Generation**: Client-side generation from existing data with fallback sample alerts

## Test Scenarios

### 1. Navigation and Page Load
**Test ID**: AL-001
**Objective**: Verify alerts page loads correctly
**Steps**:
1. Navigate to `/alerts` route
2. Verify page header displays "Kitchen Alert Center"
3. Confirm filters panel loads on the left
4. Confirm alerts table loads on the right
5. Check for proper branding elements (chef hat icons, orange theme)

**Expected Results**:
- Page loads without errors
- All UI components render properly
- Brand styling is consistent
- Loading states display appropriately

### 2. Alert Data Loading
**Test ID**: AL-002
**Objective**: Test alert data fetching and display
**Steps**:
1. Open browser developer tools
2. Navigate to alerts page
3. Monitor network requests in dev tools
4. Check console for any error messages
5. Verify alerts appear in the table

**Expected Results**:
- Queries to `data_ingestion_logs` and `integration_status` execute
- If no real alerts exist, sample alerts display
- Console shows "Generating alerts from existing data..." message
- Alert data populates the table correctly

### 3. Filtering Functionality
**Test ID**: AL-003
**Objective**: Test alert filtering by severity, status, and location
**Steps**:
1. Note the total number of alerts displayed
2. Select "Critical" severity filter
3. Verify only critical alerts show
4. Clear filters and select "Active" status
5. Verify only active alerts show
6. Test location filters if multiple locations exist
7. Test "Clear All" button functionality

**Expected Results**:
- Filters update alert count in real-time
- Alert table reflects filtered results
- Filter counts show correct numbers
- Clear All button resets all filters

### 4. Alert Selection and Bulk Actions
**Test ID**: AL-004
**Objective**: Test alert selection and bulk operations
**Steps**:
1. Select individual alerts using checkboxes
2. Verify selection count updates
3. Select all alerts using header checkbox
4. Test "Mark All Read" button
5. Test "Bulk Actions" button
6. Verify toast notifications appear

**Expected Results**:
- Individual selection works correctly
- Select all toggles all alerts
- Bulk actions trigger appropriate notifications
- Alert status updates reflect in UI

### 5. Individual Alert Actions
**Test ID**: AL-005
**Objective**: Test individual alert action buttons
**Steps**:
1. Locate action buttons in alert rows (Eye, MessageSquare, Pause)
2. Click "View" (Eye) button on an alert
3. Click "Comment" (MessageSquare) button
4. Click "Pause" button
5. Verify toast notifications for each action

**Expected Results**:
- Each action button responds correctly
- Toast notifications display appropriate messages
- Alert status updates when paused

### 6. Alert Severity and Status Icons
**Test ID**: AL-006
**Objective**: Verify correct display of severity badges and status icons
**Steps**:
1. Review alerts table for different severity levels
2. Verify color coding: Critical (red), High (orange), Medium (yellow), Low (blue)
3. Check status icons: Active (red X), Acknowledged (yellow clock), Resolved (green check)
4. Verify icon consistency across the interface

**Expected Results**:
- Severity badges display correct colors
- Status icons match alert states
- Visual indicators are consistent and clear

### 7. Walkthrough System
**Test ID**: AL-007
**Objective**: Test guided tour functionality
**Steps**:
1. Clear localStorage to simulate new user
2. Navigate to alerts page
3. Verify walkthrough starts automatically after 2 seconds
4. Progress through all walkthrough steps
5. Test "Previous" and "Next" buttons
6. Test "Skip this tour" functionality

**Expected Results**:
- Walkthrough triggers for new users
- All steps highlight correct elements
- Navigation works smoothly
- Skip option terminates walkthrough
- Completed walkthrough doesn't re-trigger

### 8. Responsive Design
**Test ID**: AL-008
**Objective**: Test responsive behavior across screen sizes
**Steps**:
1. Test on desktop (1920x1080)
2. Test on tablet (768x1024)
3. Test on mobile (375x667)
4. Verify layout adjustments
5. Check filter panel behavior on small screens

**Expected Results**:
- Layout adapts to different screen sizes
- No horizontal scrolling on mobile
- Touch targets are appropriately sized
- Filter panel remains accessible

### 9. Performance Testing
**Test ID**: AL-009
**Objective**: Verify system performance with large datasets
**Steps**:
1. Monitor page load times
2. Test filtering with large alert lists
3. Check memory usage in dev tools
4. Verify smooth scrolling and interactions

**Expected Results**:
- Page loads within 3 seconds
- Filtering operations complete quickly
- No memory leaks detected
- Smooth user interactions

### 10. Error Handling
**Test ID**: AL-010
**Objective**: Test error scenarios and fallbacks
**Steps**:
1. Disconnect from internet and reload page
2. Test with invalid Supabase connection
3. Verify error messages display appropriately
4. Check fallback to sample data when database unavailable

**Expected Results**:
- Graceful error handling
- User-friendly error messages
- Fallback data displays when needed
- No application crashes

## Data Validation

### Alert Properties
Verify each alert contains:
- `id`: Unique identifier
- `title`: Descriptive alert title
- `description`: Optional detailed description
- `severity`: One of 'critical', 'high', 'medium', 'low'
- `status`: One of 'active', 'acknowledged', 'resolved'
- `category`: Alert category (e.g., 'Data Quality', 'Sync Health')
- `created_at`: Timestamp of alert creation
- `metadata`: Additional contextual information

### Alert Generation Logic
Verify alerts are generated for:
- Failed data ingestion (status = 'failed')
- High failure rates (>15% failure rate)
- Integration errors (status = 'error')
- Sync delays (last_sync_at > 3 hours ago)

## Browser Compatibility
Test on:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Known Limitations
1. Database alerts table not yet created - using generated alerts from existing data
2. Real-time alert updates not implemented - requires manual refresh
3. Alert notifications not persistent across sessions
4. Limited alert categories based on available data sources

## Test Data Requirements
For comprehensive testing, ensure test database contains:
- Multiple entries in `data_ingestion_logs` with various statuses
- Multiple entries in `integration_status` with different sync times
- At least one failed ingestion record
- At least one integration with error status

## Success Criteria
- All test scenarios pass without critical errors
- User interface is intuitive and responsive
- Performance meets acceptable standards
- Error handling provides clear feedback
- Walkthrough system guides users effectively

## Reporting Issues
When reporting bugs, include:
- Test ID and step number
- Browser and version
- Screen size/device type
- Console error messages
- Screenshots of unexpected behavior
- Steps to reproduce the issue

---

**Document Version**: 1.0
**Last Updated**: Current Date
**Prepared for**: ChatGPT o4-mini Quality Assurance Testing
