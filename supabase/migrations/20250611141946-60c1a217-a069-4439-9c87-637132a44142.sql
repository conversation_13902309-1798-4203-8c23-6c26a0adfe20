
-- Enable RLS on all tables that don't have it
ALTER TABLE public.dim_tenant ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dim_menu_item ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dim_employee ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dim_location ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dim_date ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dim_time ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dim_order_type ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dim_payment_method ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dim_promotion ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dim_customer ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fact_sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fact_sales_line_item ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fact_labor ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fact_inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.anomaly_detection ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.predictive_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bi_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_model_performance ENABLE ROW LEVEL SECURITY;

-- Tenant dimension policies
CREATE POLICY "Users can view their tenant data" 
ON public.dim_tenant FOR SELECT TO authenticated 
USING (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Admins can manage tenant data" 
ON public.dim_tenant FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- Location dimension policies
CREATE POLICY "Users can view their tenant locations" 
ON public.dim_location FOR SELECT TO authenticated 
USING (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Admins can manage location data" 
ON public.dim_location FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- Menu item dimension policies
CREATE POLICY "Users can view their tenant menu items" 
ON public.dim_menu_item FOR SELECT TO authenticated 
USING (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Admins can manage menu items" 
ON public.dim_menu_item FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- Employee dimension policies
CREATE POLICY "Users can view their tenant employees" 
ON public.dim_employee FOR SELECT TO authenticated 
USING (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Admins can manage employee data" 
ON public.dim_employee FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- Customer dimension policies
CREATE POLICY "Users can view their tenant customers" 
ON public.dim_customer FOR SELECT TO authenticated 
USING (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Admins can manage customer data" 
ON public.dim_customer FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- Date dimension - shared across tenants
CREATE POLICY "Authenticated users can view date dimension" 
ON public.dim_date FOR SELECT TO authenticated 
USING (true);

CREATE POLICY "Admins can insert date dimension" 
ON public.dim_date FOR INSERT TO authenticated 
WITH CHECK (public.is_user_admin());

CREATE POLICY "Admins can update date dimension" 
ON public.dim_date FOR UPDATE TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

CREATE POLICY "Admins can delete date dimension" 
ON public.dim_date FOR DELETE TO authenticated 
USING (public.is_user_admin());

-- Time dimension - shared across tenants
CREATE POLICY "Authenticated users can view time dimension" 
ON public.dim_time FOR SELECT TO authenticated 
USING (true);

CREATE POLICY "Admins can insert time dimension" 
ON public.dim_time FOR INSERT TO authenticated 
WITH CHECK (public.is_user_admin());

CREATE POLICY "Admins can update time dimension" 
ON public.dim_time FOR UPDATE TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

CREATE POLICY "Admins can delete time dimension" 
ON public.dim_time FOR DELETE TO authenticated 
USING (public.is_user_admin());

-- Order type dimension policies
CREATE POLICY "Users can view their tenant order types" 
ON public.dim_order_type FOR SELECT TO authenticated 
USING (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Admins can manage order types" 
ON public.dim_order_type FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- Payment method dimension policies
CREATE POLICY "Users can view their tenant payment methods" 
ON public.dim_payment_method FOR SELECT TO authenticated 
USING (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Admins can manage payment methods" 
ON public.dim_payment_method FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- Promotion dimension policies
CREATE POLICY "Users can view their tenant promotions" 
ON public.dim_promotion FOR SELECT TO authenticated 
USING (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Admins can manage promotions" 
ON public.dim_promotion FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- Sales fact table policies
CREATE POLICY "Users can view their tenant sales data" 
ON public.fact_sales FOR SELECT TO authenticated 
USING (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Admins can manage sales data" 
ON public.fact_sales FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- Sales line item fact table policies
CREATE POLICY "Users can view their tenant sales line items" 
ON public.fact_sales_line_item FOR SELECT TO authenticated 
USING (
  EXISTS (
    SELECT 1 FROM public.fact_sales fs 
    WHERE fs.sales_key = fact_sales_line_item.sales_key 
    AND fs.tenant_key = public.get_user_tenant_key()
  )
);

CREATE POLICY "Admins can manage sales line items" 
ON public.fact_sales_line_item FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- Labor fact table policies
CREATE POLICY "Users can view their tenant labor data" 
ON public.fact_labor FOR SELECT TO authenticated 
USING (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Admins can manage labor data" 
ON public.fact_labor FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- Inventory fact table policies
CREATE POLICY "Users can view their tenant inventory data" 
ON public.fact_inventory FOR SELECT TO authenticated 
USING (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Admins can manage inventory data" 
ON public.fact_inventory FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- AI insights policies
CREATE POLICY "Users can view their tenant AI insights" 
ON public.ai_insights FOR SELECT TO authenticated 
USING (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Users can update their tenant AI insights" 
ON public.ai_insights FOR UPDATE TO authenticated 
USING (tenant_key = public.get_user_tenant_key()) 
WITH CHECK (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Admins can manage AI insights" 
ON public.ai_insights FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- Anomaly detection policies
CREATE POLICY "Users can view their tenant anomaly detections" 
ON public.anomaly_detection FOR SELECT TO authenticated 
USING (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Users can update their tenant anomaly detections" 
ON public.anomaly_detection FOR UPDATE TO authenticated 
USING (tenant_key = public.get_user_tenant_key()) 
WITH CHECK (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Admins can manage anomaly detections" 
ON public.anomaly_detection FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- Predictive analytics policies
CREATE POLICY "Users can view their tenant predictive analytics" 
ON public.predictive_analytics FOR SELECT TO authenticated 
USING (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Admins can manage predictive analytics" 
ON public.predictive_analytics FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- BI metrics policies
CREATE POLICY "Users can view their tenant BI metrics" 
ON public.bi_metrics FOR SELECT TO authenticated 
USING (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Admins can manage BI metrics" 
ON public.bi_metrics FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- AI model performance policies
CREATE POLICY "Users can view their tenant AI model performance" 
ON public.ai_model_performance FOR SELECT TO authenticated 
USING (tenant_key = public.get_user_tenant_key());

CREATE POLICY "Admins can manage AI model performance" 
ON public.ai_model_performance FOR ALL TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());
