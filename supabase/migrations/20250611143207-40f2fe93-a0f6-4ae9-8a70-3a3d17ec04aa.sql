
-- Add owner_id column to master data tables for proper RLS
ALTER TABLE public.master_menu_items 
ADD COLUMN owner_id uuid REFERENCES auth.users(id);

ALTER TABLE public.master_locations 
ADD COLUMN owner_id uuid REFERENCES auth.users(id);

ALTER TABLE public.master_employees 
ADD COLUMN owner_id uuid REFERENCES auth.users(id);

ALTER TABLE public.master_menu_categories 
ADD COLUMN owner_id uuid REFERENCES auth.users(id);

ALTER TABLE public.master_day_parts 
ADD COLUMN owner_id uuid REFERENCES auth.users(id);

ALTER TABLE public.master_ingredients_supplies 
ADD COLUMN owner_id uuid REFERENCES auth.users(id);

ALTER TABLE public.master_restaurant_operators 
ADD COLUMN owner_id uuid REFERENCES auth.users(id);

ALTER TABLE public.master_survey_questions 
ADD COLUMN owner_id uuid REFERENCES auth.users(id);

-- Enable RLS on master data tables
ALTER TABLE public.master_menu_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.master_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.master_employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.master_menu_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.master_day_parts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.master_ingredients_supplies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.master_restaurant_operators ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.master_survey_questions ENABLE ROW LEVEL SECURITY;

-- Create secure RLS policies for master_menu_items
CREATE POLICY "Authenticated users can select from master_menu_items" 
ON public.master_menu_items 
FOR SELECT 
TO authenticated 
USING (true);

CREATE POLICY "Authenticated users can insert into master_menu_items" 
ON public.master_menu_items 
FOR INSERT 
TO authenticated 
WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can update their own master_menu_items" 
ON public.master_menu_items 
FOR UPDATE 
TO authenticated 
USING (owner_id = auth.uid()) 
WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can delete their own master_menu_items" 
ON public.master_menu_items 
FOR DELETE 
TO authenticated 
USING (owner_id = auth.uid());

-- Create secure RLS policies for master_locations
CREATE POLICY "Authenticated users can select from master_locations" 
ON public.master_locations 
FOR SELECT 
TO authenticated 
USING (true);

CREATE POLICY "Authenticated users can insert into master_locations" 
ON public.master_locations 
FOR INSERT 
TO authenticated 
WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can update their own master_locations" 
ON public.master_locations 
FOR UPDATE 
TO authenticated 
USING (owner_id = auth.uid()) 
WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can delete their own master_locations" 
ON public.master_locations 
FOR DELETE 
TO authenticated 
USING (owner_id = auth.uid());

-- Create secure RLS policies for master_employees
CREATE POLICY "Authenticated users can select from master_employees" 
ON public.master_employees 
FOR SELECT 
TO authenticated 
USING (true);

CREATE POLICY "Authenticated users can insert into master_employees" 
ON public.master_employees 
FOR INSERT 
TO authenticated 
WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can update their own master_employees" 
ON public.master_employees 
FOR UPDATE 
TO authenticated 
USING (owner_id = auth.uid()) 
WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can delete their own master_employees" 
ON public.master_employees 
FOR DELETE 
TO authenticated 
USING (owner_id = auth.uid());

-- Create secure RLS policies for master_menu_categories
CREATE POLICY "Authenticated users can select from master_menu_categories" 
ON public.master_menu_categories 
FOR SELECT 
TO authenticated 
USING (true);

CREATE POLICY "Authenticated users can insert into master_menu_categories" 
ON public.master_menu_categories 
FOR INSERT 
TO authenticated 
WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can update their own master_menu_categories" 
ON public.master_menu_categories 
FOR UPDATE 
TO authenticated 
USING (owner_id = auth.uid()) 
WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can delete their own master_menu_categories" 
ON public.master_menu_categories 
FOR DELETE 
TO authenticated 
USING (owner_id = auth.uid());

-- Create secure RLS policies for master_day_parts
CREATE POLICY "Authenticated users can select from master_day_parts" 
ON public.master_day_parts 
FOR SELECT 
TO authenticated 
USING (true);

CREATE POLICY "Authenticated users can insert into master_day_parts" 
ON public.master_day_parts 
FOR INSERT 
TO authenticated 
WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can update their own master_day_parts" 
ON public.master_day_parts 
FOR UPDATE 
TO authenticated 
USING (owner_id = auth.uid()) 
WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can delete their own master_day_parts" 
ON public.master_day_parts 
FOR DELETE 
TO authenticated 
USING (owner_id = auth.uid());

-- Create secure RLS policies for master_ingredients_supplies
CREATE POLICY "Authenticated users can select from master_ingredients_supplies" 
ON public.master_ingredients_supplies 
FOR SELECT 
TO authenticated 
USING (true);

CREATE POLICY "Authenticated users can insert into master_ingredients_supplies" 
ON public.master_ingredients_supplies 
FOR INSERT 
TO authenticated 
WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can update their own master_ingredients_supplies" 
ON public.master_ingredients_supplies 
FOR UPDATE 
TO authenticated 
USING (owner_id = auth.uid()) 
WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can delete their own master_ingredients_supplies" 
ON public.master_ingredients_supplies 
FOR DELETE 
TO authenticated 
USING (owner_id = auth.uid());

-- Create secure RLS policies for master_restaurant_operators
CREATE POLICY "Authenticated users can select from master_restaurant_operators" 
ON public.master_restaurant_operators 
FOR SELECT 
TO authenticated 
USING (true);

CREATE POLICY "Authenticated users can insert into master_restaurant_operators" 
ON public.master_restaurant_operators 
FOR INSERT 
TO authenticated 
WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can update their own master_restaurant_operators" 
ON public.master_restaurant_operators 
FOR UPDATE 
TO authenticated 
USING (owner_id = auth.uid()) 
WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can delete their own master_restaurant_operators" 
ON public.master_restaurant_operators 
FOR DELETE 
TO authenticated 
USING (owner_id = auth.uid());

-- Create secure RLS policies for master_survey_questions
CREATE POLICY "Authenticated users can select from master_survey_questions" 
ON public.master_survey_questions 
FOR SELECT 
TO authenticated 
USING (true);

CREATE POLICY "Authenticated users can insert into master_survey_questions" 
ON public.master_survey_questions 
FOR INSERT 
TO authenticated 
WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can update their own master_survey_questions" 
ON public.master_survey_questions 
FOR UPDATE 
TO authenticated 
USING (owner_id = auth.uid()) 
WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can delete their own master_survey_questions" 
ON public.master_survey_questions 
FOR DELETE 
TO authenticated 
USING (owner_id = auth.uid());

-- Also enable RLS on other tables that should have it
ALTER TABLE public.data_mappings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.unmapped_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.source_systems ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mapping_rules ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for data_mappings
CREATE POLICY "Authenticated users can view data mappings" 
ON public.data_mappings 
FOR SELECT 
TO authenticated 
USING (true);

CREATE POLICY "Authenticated users can create data mappings" 
ON public.data_mappings 
FOR INSERT 
TO authenticated 
WITH CHECK (mapped_by = auth.uid());

CREATE POLICY "Users can update their own data mappings" 
ON public.data_mappings 
FOR UPDATE 
TO authenticated 
USING (mapped_by = auth.uid()) 
WITH CHECK (mapped_by = auth.uid());

CREATE POLICY "Users can delete their own data mappings" 
ON public.data_mappings 
FOR DELETE 
TO authenticated 
USING (mapped_by = auth.uid());

-- Create RLS policies for unmapped_items
CREATE POLICY "Authenticated users can view unmapped items" 
ON public.unmapped_items 
FOR SELECT 
TO authenticated 
USING (true);

CREATE POLICY "System can manage unmapped items" 
ON public.unmapped_items 
FOR ALL 
TO authenticated 
USING (true) 
WITH CHECK (true);

-- Create RLS policies for source_systems
CREATE POLICY "Authenticated users can view source systems" 
ON public.source_systems 
FOR SELECT 
TO authenticated 
USING (true);

CREATE POLICY "Admins can manage source systems" 
ON public.source_systems 
FOR ALL 
TO authenticated 
USING (public.is_user_admin()) 
WITH CHECK (public.is_user_admin());

-- Create RLS policies for mapping_rules
CREATE POLICY "Authenticated users can view mapping rules" 
ON public.mapping_rules 
FOR SELECT 
TO authenticated 
USING (true);

CREATE POLICY "Users can create mapping rules" 
ON public.mapping_rules 
FOR INSERT 
TO authenticated 
WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can update their own mapping rules" 
ON public.mapping_rules 
FOR UPDATE 
TO authenticated 
USING (created_by = auth.uid()) 
WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can delete their own mapping rules" 
ON public.mapping_rules 
FOR DELETE 
TO authenticated 
USING (created_by = auth.uid());
