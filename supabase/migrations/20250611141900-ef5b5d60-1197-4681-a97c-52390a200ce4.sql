
-- Create security definer functions to avoid RLS infinite recursion issues

-- Function to get the current user's tenant key
CREATE OR REPLACE FUNCTION public.get_user_tenant_key()
RETURNS INTEGER AS $$
DECLARE
  user_tenant_key INTEGER;
BEGIN
  -- Get the tenant_key from the user's company through profiles
  SELECT c.id::text::integer INTO user_tenant_key
  FROM public.profiles p
  JOIN public.companies c ON p.company_id = c.id
  WHERE p.id = auth.uid();
  
  -- Default to tenant_key 1 if no company found (for demo purposes)
  RETURN COALESCE(user_tenant_key, 1);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER STABLE;

-- Function to check if current user is an admin
CREATE OR REPLACE FUNCTION public.is_user_admin()
RETURNS BOOLEAN AS $$
DECLARE
  user_role TEXT;
BEGIN
  SELECT role INTO user_role
  FROM public.profiles
  WHERE id = auth.uid();
  
  -- Consider CEO, COO, and <PERSON> Admin as admin roles
  RETURN user_role IN ('CEO', 'COO', 'Data Admin');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER STABLE;

-- <PERSON> execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION public.get_user_tenant_key() TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_user_admin() TO authenticated;
