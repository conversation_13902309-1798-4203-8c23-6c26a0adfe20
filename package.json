{"name": "convx-portal-journey", "private": true, "version": "0.0.0", "type": "module", "packageManager": "pnpm@10.12.3", "scripts": {"dev": "pnpm --filter @convx/web dev", "build": "pnpm --filter @convx/web build", "lint": "pnpm --filter @convx/web lint", "test": "pnpm --filter @convx/web test", "preview": "pnpm --filter @convx/web preview", "prepare": "husky"}, "devDependencies": {"turbo": "^2.5.4", "prettier": "^3.4.2", "@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "husky": "^9.1.7", "lint-staged": "^15.3.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}}