# Sprint 2 Tickets: Core Authentication & Database

**Sprint Duration:** Week 2  
**Sprint Goal:** Implement core authentication system and database foundation  
**Total Capacity:** 170 hours  
**Estimated Story Points:** 42 points

---

## XDPA-008: AWS Cognito Integration Setup
**Story Points:** 8  
**Assignee:** Lead Full-stack Developer  
**Type:** Story  
**Priority:** Highest

### Description
As a developer, I need to integrate AWS Cognito for user authentication so that users can securely log in and access the application.

### Acceptance Criteria
- [ ] Cognito User Pool configured with appropriate settings
- [ ] Cognito App Client created with correct permissions
- [ ] JWT token validation implemented in FastAPI
- [ ] Frontend authentication service created
- [ ] Token refresh mechanism implemented
- [ ] Logout functionality working

### Technical Requirements
- **User Pool Settings:**
  - Email as username
  - Password policy: min 8 chars, uppercase, lowercase, number
  - MFA optional (SMS/TOTP)
  - Account recovery via email
- **App Client:**
  - Authorization code grant flow
  - Refresh token rotation enabled
  - Token expiration: Access (1 hour), Refresh (30 days)

### API Endpoints
```python
# Backend endpoints to implement
POST /v1/auth/login      # Cognito login redirect
POST /v1/auth/callback   # Handle Cognito callback
POST /v1/auth/refresh    # Refresh access token
POST /v1/auth/logout     # Logout and invalidate tokens
GET  /v1/auth/me         # Get current user info
```

### Definition of Done
- [ ] User can log in via Cognito hosted UI
- [ ] JWT tokens are properly validated
- [ ] Token refresh works automatically
- [ ] User information is accessible via API
- [ ] Logout clears all tokens
- [ ] Error handling for auth failures

---

## XDPA-009: Database Schema Implementation
**Story Points:** 13  
**Assignee:** Full-stack Developer #1  
**Type:** Task  
**Priority:** Highest

### Description
Implement the core database schema for the Restaurant Data Platform including all necessary tables, relationships, and indexes.

### Acceptance Criteria
- [ ] All core tables created with proper data types
- [ ] Foreign key relationships established
- [ ] Indexes created for performance optimization
- [ ] Database migration system implemented
- [ ] Seed data for development environment
- [ ] Database connection pooling configured

### Database Schema
```sql
-- Companies
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Users
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cognito_sub VARCHAR(255) UNIQUE NOT NULL,
    company_id UUID REFERENCES companies(id),
    email VARCHAR(255) NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    role VARCHAR(50) NOT NULL DEFAULT 'BusinessAdmin',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Profiles
CREATE TABLE user_profiles (
    user_id UUID PRIMARY KEY REFERENCES users(id),
    timezone VARCHAR(50) DEFAULT 'UTC',
    notification_preferences JSONB DEFAULT '{}',
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Integration Types
CREATE TABLE integration_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    logo_url VARCHAR(500),
    config_schema JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Integrations
CREATE TABLE integrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID REFERENCES companies(id),
    type_id UUID REFERENCES integration_types(id),
    name VARCHAR(255) NOT NULL,
    credentials_encrypted TEXT,
    status VARCHAR(50) DEFAULT 'inactive',
    last_sync TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Locations
CREATE TABLE locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID REFERENCES companies(id),
    name VARCHAR(255) NOT NULL,
    address TEXT,
    timezone VARCHAR(50) DEFAULT 'UTC',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Feature Flags
CREATE TABLE feature_flags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    flag_name VARCHAR(255) NOT NULL,
    is_enabled BOOLEAN DEFAULT false,
    company_id UUID REFERENCES companies(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(flag_name, company_id)
);
```

### Indexes and Performance
```sql
-- Performance indexes
CREATE INDEX idx_users_cognito_sub ON users(cognito_sub);
CREATE INDEX idx_users_company_id ON users(company_id);
CREATE INDEX idx_integrations_company_id ON integrations(company_id);
CREATE INDEX idx_locations_company_id ON locations(company_id);
CREATE INDEX idx_feature_flags_company_flag ON feature_flags(company_id, flag_name);
```

### Definition of Done
- [ ] All tables created successfully
- [ ] Foreign key constraints work correctly
- [ ] Indexes improve query performance
- [ ] Migration scripts run without errors
- [ ] Seed data populates correctly
- [ ] Connection pooling is configured

---

## XDPA-010: FastAPI Application Structure
**Story Points:** 8  
**Assignee:** Full-stack Developer #2  
**Type:** Task  
**Priority:** High

### Description
Create the foundational FastAPI application structure with proper organization, middleware, and configuration management.

### Acceptance Criteria
- [ ] FastAPI app with modular router structure
- [ ] Database connection and session management
- [ ] Middleware for CORS, security headers, and logging
- [ ] Environment configuration management
- [ ] Error handling and exception middleware
- [ ] Health check endpoints

### Application Structure
```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI app initialization
│   ├── config.py            # Configuration management
│   ├── database.py          # Database connection
│   ├── dependencies.py      # Common dependencies
│   ├── middleware/          # Custom middleware
│   ├── models/              # SQLAlchemy models
│   ├── schemas/             # Pydantic schemas
│   ├── routers/             # API route handlers
│   ├── services/            # Business logic
│   └── utils/               # Utility functions
├── tests/                   # Test files
├── alembic/                 # Database migrations
├── requirements.txt         # Python dependencies
└── Dockerfile              # Container configuration
```

### Core Configuration
```python
# app/config.py
from pydantic import BaseSettings

class Settings(BaseSettings):
    database_url: str
    redis_url: str
    cognito_user_pool_id: str
    cognito_client_id: str
    jwt_secret_key: str
    environment: str = "development"
    debug: bool = False
    
    class Config:
        env_file = ".env"
```

### Definition of Done
- [ ] FastAPI application starts without errors
- [ ] Database connection is established
- [ ] All middleware functions correctly
- [ ] Configuration loads from environment
- [ ] Health check endpoint returns 200
- [ ] Error handling works as expected

---

## XDPA-011: React Application Foundation
**Story Points:** 5  
**Assignee:** Full-stack Developer #1  
**Type:** Task  
**Priority:** High

### Description
Set up the React application foundation with TypeScript, routing, and basic component structure using shadcn/ui.

### Acceptance Criteria
- [ ] React 18+ application with TypeScript
- [ ] Vite build configuration optimized
- [ ] React Router v6 setup with protected routes
- [ ] shadcn/ui components installed and configured
- [ ] Tailwind CSS configured with design tokens
- [ ] Basic layout components created

### Application Structure
```
frontend/
├── src/
│   ├── components/          # Reusable components
│   │   ├── ui/             # shadcn/ui components
│   │   ├── layout/         # Layout components
│   │   └── common/         # Common components
│   ├── pages/              # Page components
│   ├── hooks/              # Custom React hooks
│   ├── services/           # API services
│   ├── utils/              # Utility functions
│   ├── types/              # TypeScript types
│   ├── styles/             # Global styles
│   ├── App.tsx             # Main app component
│   └── main.tsx            # App entry point
├── public/                 # Static assets
├── package.json
├── vite.config.ts
├── tailwind.config.js
└── tsconfig.json
```

### Core Components
```typescript
// Basic layout structure
- AppLayout: Main application layout
- Header: Navigation and user menu
- Sidebar: Main navigation menu
- Footer: Application footer
- LoadingSpinner: Loading states
- ErrorBoundary: Error handling
```

### Definition of Done
- [ ] React application builds and runs
- [ ] TypeScript compilation works without errors
- [ ] Routing navigates between pages
- [ ] shadcn/ui components render correctly
- [ ] Tailwind styles apply properly
- [ ] Basic layout is responsive

---

## XDPA-012: Authentication Context and Hooks
**Story Points:** 5  
**Assignee:** Lead Full-stack Developer  
**Type:** Story  
**Priority:** High

### Description
As a developer, I need React authentication context and hooks so that I can manage user authentication state throughout the application.

### Acceptance Criteria
- [ ] Authentication context provider created
- [ ] Custom hooks for auth operations
- [ ] Protected route component
- [ ] Token storage and management
- [ ] Automatic token refresh
- [ ] Login/logout state management

### Implementation
```typescript
// Authentication context
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (token: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}

// Custom hooks
const useAuth = () => useContext(AuthContext);
const useRequireAuth = () => { /* Redirect if not authenticated */ };

// Protected route component
const ProtectedRoute = ({ children }: { children: ReactNode }) => {
  const { isAuthenticated, isLoading } = useAuth();
  // Handle authentication logic
};
```

### Definition of Done
- [ ] Authentication context works correctly
- [ ] Protected routes redirect unauthenticated users
- [ ] Token refresh happens automatically
- [ ] User state persists across page reloads
- [ ] Logout clears all authentication state
- [ ] Loading states are handled properly

---

## XDPA-013: Basic API Client Setup
**Story Points:** 3  
**Assignee:** Full-stack Developer #2  
**Type:** Task  
**Priority:** Medium

### Description
Create a configured HTTP client for API communication with proper error handling, authentication, and request/response interceptors.

### Acceptance Criteria
- [ ] Axios client configured with base URL
- [ ] Request interceptor adds authentication headers
- [ ] Response interceptor handles common errors
- [ ] Retry logic for failed requests
- [ ] TypeScript types for API responses
- [ ] Error handling utilities

### API Client Configuration
```typescript
// API client setup
const apiClient = axios.create({
  baseURL: process.env.VITE_API_BASE_URL,
  timeout: 10000,
});

// Request interceptor
apiClient.interceptors.request.use((config) => {
  const token = getAuthToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle token refresh or logout
    }
    return Promise.reject(error);
  }
);
```

### Definition of Done
- [ ] API client makes successful requests
- [ ] Authentication headers are added automatically
- [ ] Error responses are handled gracefully
- [ ] Retry logic works for network failures
- [ ] TypeScript types are properly defined
- [ ] Error utilities are reusable

---

## Sprint 2 Summary

### Total Story Points: 42
### Team Allocation:
- **Lead Full-stack Developer:** 40 hours (XDPA-008, XDPA-012)
- **Full-stack Developer #1:** 40 hours (XDPA-009, XDPA-011)
- **Full-stack Developer #2:** 40 hours (XDPA-010, XDPA-013)
- **QA Engineer:** 30 hours (Testing authentication flows and database operations)
- **Project Manager:** 20 hours (Sprint coordination and stakeholder updates)

### Sprint Goals:
1. ✅ Implement AWS Cognito authentication
2. ✅ Create complete database schema
3. ✅ Establish FastAPI application structure
4. ✅ Set up React application foundation
5. ✅ Create authentication context and API client

### Dependencies:
- AWS Cognito User Pool setup (coordinated with DevOps)
- Database instance availability
- Environment configuration from Sprint 1

### Risks:
- **Cognito Configuration Complexity:** Plan for additional time if needed
- **Database Migration Issues:** Test thoroughly in development
- **Authentication Flow Complexity:** Break down into smaller tasks if needed

### Success Criteria:
- [ ] Users can authenticate via Cognito
- [ ] Database schema is complete and functional
- [ ] FastAPI application serves basic endpoints
- [ ] React application renders with authentication
- [ ] API client communicates successfully with backend
