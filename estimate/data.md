now you have to merge
Admin View - Question Bank Management
Front-End Features:

Master Question Bank Dashboard with search, filter, and bulk operations across all questions

Question Builder Interface with rich text editor for stems, drag-and-drop option ordering, and multimedia support

Bulk Import Wizard supporting CSV, Excel, Word docs, and plain text with live preview and validation

AI Question Generator with paragraph-to-MCQ conversion, difficulty auto-detection, and batch processing

Version Control System showing question edit history, diff viewer, and rollback capabilities

Quality Assurance Panel for reviewing flagged questions, duplicate detection, and content moderation

Analytics Dashboard showing question performance metrics, usage statistics, and difficulty calibration

User Management Interface for role assignments, permissions, and access control

Taxonomy Management for subjects, topics, domains, and skill categorization

Import/Export Tools for question migration, backup creation, and cross-platform compatibility

Back-End Infrastructure:

Admin Authentication System with multi-factor authentication and session management

Question CRUD API with comprehensive validation, sanitization, and audit logging

AI Integration Service connecting to OpenAI/Claude for question generation and enhancement

File Processing Pipeline handling multiple formats with virus scanning and content extraction

Duplicate Detection Engine using semantic similarity and exact match algorithms

Search Indexing Service with full-text search, faceted filtering, and relevance scoring

Version Control Database tracking all changes with metadata and rollback capabilities

Content Moderation System with automated flagging and manual review workflows

Analytics Engine calculating question statistics, performance metrics, and usage patterns

Backup and Recovery System with automated daily backups and disaster recovery procedures

Tutor View - Content Creation & Management
Front-End Features:

Personal Question Library showing tutor-created questions with ownership tracking

Collaborative Question Builder with template library and subject-specific frameworks

Student Performance Insights showing how their questions perform across different student groups

Question Assignment Tools for creating custom practice sets and targeted assessments

Content Sharing Interface for contributing questions to the main bank with approval workflow

Difficulty Calibration Tools based on student performance data and peer feedback

Question Enhancement Suggestions powered by AI analysis of student responses

Curriculum Mapping Tools aligning questions with learning objectives and standards

Peer Review System for collaborative question improvement and quality assurance

Usage Analytics showing which questions are most effective for different learning outcomes

Back-End Infrastructure:

Tutor Authentication with role-based permissions and content ownership tracking

Question Ownership System managing creation rights, sharing permissions, and attribution

Collaboration Framework enabling question sharing, peer review, and version control

Performance Analytics API providing insights into question effectiveness and student engagement

Content Approval Workflow with automated checks and manual review processes

AI-Powered Suggestions analyzing question quality and recommending improvements

Integration with Student Data for personalized question recommendations and difficulty adjustment

Notification System alerting tutors to question performance issues and improvement opportunities

Student View - Practice & Assessment
Front-End Features:

Practice Question Interface with clean, distraction-free design and accessibility features

Personalized Question Recommendations based on performance history and learning gaps

Progress Tracking Dashboard showing mastery levels across different topics and domains

Mistake Analysis Tools highlighting common errors and providing targeted practice

Bookmark and Review System for saving difficult questions and creating custom study sets

Performance Comparison showing progress relative to peers and target scores

Study Plan Generator creating personalized practice schedules based on test dates and goals

Explanation and Feedback Interface with detailed solutions and learning resources

Mobile-Optimized Practice for on-the-go studying with offline capability

Gamification Elements including streaks, achievements, and progress celebrations

Back-End Infrastructure:

Student Authentication with secure login and privacy protection

Adaptive Question Selection using machine learning to optimize question difficulty and topics

Performance Tracking Database storing detailed attempt history and learning analytics

Recommendation Engine analyzing student data to suggest optimal practice questions

Progress Calculation System determining mastery levels and identifying knowledge gaps

Personalization Algorithms customizing the learning experience based on individual needs

Mobile API supporting offline practice and seamless synchronization

Privacy Protection ensuring student data security and GDPR compliance

Cross-Role Infrastructure Requirements
Shared Systems:

Multi-Tenant Architecture supporting role-based access control and data isolation

Real-Time Synchronization ensuring consistent data across all user interfaces

Comprehensive Audit Logging tracking all actions for security and compliance

Scalable Database Design handling millions of questions and user interactions

Content Delivery Network for fast loading of multimedia question content

API Gateway managing authentication, rate limiting, and request routing

Monitoring and Alerting for system health, performance, and security incidents

Data Backup and Recovery with automated testing and disaster recovery procedures

AI-Powered Features:

Question Generation Engine creating high-quality MCQs from source material

Difficulty Prediction Model automatically assigning difficulty levels to new questions

Content Quality Analyzer identifying potential issues with question clarity and fairness

Plagiarism Detection ensuring originality of user-contributed content

Performance Prediction forecasting how questions will perform with different student populations

Adaptive Learning Algorithms personalizing the learning experience for each student

This comprehensive system ensures that each user role has the appropriate tools and interfaces while maintaining data integrity, security, and optimal performance across the entire platform.

and Authentication and Accounts
Front: sign-up page, strength meter, social buttons, reset UI, two-factor modals, profile editor, session manager. Md
Back: enable Supabase Auth providers, password reset flow, MFA endpoints, profile CRUD, GDPR export service. Md
Prerequisites: Role-based access control system (student/tutor/admin), admin dashboard for user management, email service configuration, social provider app registrations, GDPR compliance framework, user onboarding flow design

Personal Dashboard
Front: score gauge, target slider, streak badge, countdown, quick links, daily-goal ring, progress chip. Md
Back: dashboard RPC, streak timer job, push/email service for daily goal. Sm-Md
Prerequisites: User preference system for customizable targets, notification service setup, streak calculation logic, daily goal tracking system, timezone handling for global users

Section and Domain Progress Widgets
Front: two circular charts, color-coded percentage label, domain bar list with drill-down. Sm
Back: nightly aggregate job, section_stats view. Sm
Prerequisites: Domain taxonomy definition, scoring algorithm design, performance benchmarking system, data visualization standards, progress calculation methodology

Practice Launcher
Front: filter panel, difficulty slider, question-count picker, start button, ctrl+G shortcut. Md
Back: pick_questions function with RLS, preview cap logic. Md
Prerequisites: Question difficulty calibration system, adaptive question selection algorithm, session management, practice mode configurations, accessibility features for keyboard navigation

Question Solving View
Front: passage pane with scroll memory, choice grid with keyboard shortcuts, strikethrough toggle, timer, nav buttons. Lg
Back: attempts insert endpoint, realtime socket for toast, autosave trigger. Md
Prerequisites: Tutor content management panel for question creation, rich text editor for passages, question validation system, auto-save mechanism design, session state management, accessibility compliance for screen readers

Immediate Scoring
Front: correct or wrong toast, streak animation, live score refresh. Sm
Back: handle_attempt trigger updating user_stats and sending broadcast. Sm
Prerequisites: Scoring algorithm validation, real-time notification system, streak bonus calculation logic, performance impact assessment

Explanations and Retry
Front: video player, step text, retry button, helpful toggle. Sm
Back: signed video URL helper, retry queue logic. Sm
Prerequisites: Video content management system for tutors, CDN setup for video delivery, content moderation workflow, explanation quality assurance process, video transcription service

Flagging and Status
Front: flag icon, status badges, tooltip. Sm
Back: flag field update, latest_attempts view. Sm
Prerequisites: Admin moderation panel for flagged content review, automated flagging detection, content quality monitoring system, tutor notification system for flagged questions

Review Center
Front: search bar, filter chips, hide-answer toggle, bulk actions, table with sort, retry inline. Md
Back: FT search index, review query endpoint. Sm
Prerequisites: Search indexing strategy, performance optimization for large datasets, bulk operation handling, data export capabilities

Performance Analytics
Front: domain bar chart, progress line chart, heat map, CSV export. Md
Back: score_history snapshots, analytic views, export function. Md
Prerequisites: Analytics dashboard for tutors and admins, data retention policies, privacy controls for analytics, comparative benchmarking system, automated insights generation

Checkout and Payment
Front: paywall overlay, checkout button, receipt screen, money-back request form. Sm
Back: Stripe checkout session, webhook, premium toggle, refund handler. Md
Prerequisites: Admin financial dashboard, subscription management system, tax calculation service, fraud detection, payment method validation, refund approval workflow

Product Detail Landing
Front: benefit tiles, testimonial carousel, FAQ accordion, buy button. Sm
Back: testimonials endpoint. Tiny
Prerequisites: Content management system for marketing materials, testimonial approval workflow, A/B testing framework, SEO optimization tools, conversion tracking

Leaderboard
Front: real-time table, highlight row, filters, rank titles, points-to-next bar. Md
Back: leaderboard view, delta broadcast. Sm
Prerequisites: Admin leaderboard management tools, ranking algorithm design, privacy settings for leaderboard participation, anti-cheating measures, seasonal leaderboard resets

User Messaging
Front: conversation list, rich text box, typing indicator, block/report UI, notifications. Lg
Back: conversations and messages with RLS, push notification hook. Lg
Prerequisites: Moderation tools for admins, spam detection system, message encryption, user safety features, automated content filtering, tutor-student communication guidelines

Study Recommendations
Front: daily practice card, weak-area hints. Sm
Back: generate_plan cron, daily_tasks table. Md
Prerequisites: Tutor curriculum management system, machine learning model for personalization, learning path optimization, difficulty progression algorithms, performance prediction models

Gamification
Front: streak calendar, XP bar, achievements wall with share sheet. Md
Back: achievement thresholds, trigger, push celebration. Sm
Prerequisites: Admin gamification control panel, achievement design system, social sharing integration, reward distribution mechanism, engagement analytics

Motivational Nudges
Front: dynamic banner, confetti component. Sm
Back: nudge_inactive_users cron, weekly digest email. Sm
Prerequisites: Admin campaign management tools, user engagement scoring, personalized messaging system, email template management, A/B testing for nudge effectiveness

Unlimited Attempts and Reset
Front: reset progress button and confirmation. Tiny
Back: archive-then-delete procedure, stats recount. Sm
Prerequisites: Admin data management tools, data backup verification, progress recovery system, audit trail for resets, user consent management

Priority Support
Front: help-desk widget load, ticket form. Tiny
Back: support_tickets table, forwarder to Zendesk. Sm
Prerequisites: Admin support dashboard, ticket routing system, SLA management, knowledge base integration, escalation procedures, support agent training materials

Technical and Data Infrastructure
DevOps: nightly backups, WAL streaming, BigQuery export, monitoring dashboards. XL
Prerequisites: Comprehensive admin monitoring suite, disaster recovery procedures, data governance framework, security audit system, performance monitoring alerts, compliance reporting tools, multi-tenant architecture for different user roles, API rate limiting and security, automated testing pipeline, staging environment management