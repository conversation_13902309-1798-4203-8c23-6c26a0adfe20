
# Comprehensive Codebase Package for GPT o1-mini QA Testing

## Project Overview

**CONVX Data Kitchen** is a comprehensive restaurant data management platform built with React, TypeScript, Tailwind CSS, and Supabase. The platform enables restaurant operators to centralize, map, and analyze data from multiple sources including POS systems, delivery platforms, social media, and more.

### Key Features
- Multi-step onboarding flow for new users and companies
- Master data mapping system with 15+ data types
- Integration wizard for connecting external systems
- Real-time alerts and monitoring system
- Executive dashboard with business intelligence
- User management with role-based access
- Guided walkthrough system across all pages

### Technology Stack
- **Frontend**: React 18, TypeScript, Vite
- **UI Framework**: Tailwind CSS, shadcn/ui components
- **State Management**: TanStack React Query
- **Backend**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Routing**: React Router DOM v6
- **Icons**: Lucide React

## Application Architecture

### Main Application Structure

```typescript
// App.tsx - Main application entry point
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { OnboardingProvider } from "@/contexts/OnboardingContext";
import { AuthProvider } from "@/hooks/useAuth";
import { ProtectedRoute } from "@/components/ProtectedRoute";

// Main pages
import Index from "./pages/Index";
import MasterDataMapping from "./pages/MasterDataMapping";
import Integrations from "./pages/Integrations";
import Alerts from "./pages/Alerts";
import Settings from "./pages/Settings";
import UserProfile from "./pages/UserProfile";
import Auth from "./pages/Auth";

// Onboarding flow (6 steps)
import WelcomeNewUser from "./pages/onboarding/WelcomeNewUser";
import CreateCompany from "./pages/onboarding/CreateCompany";
import AddLocations from "./pages/onboarding/AddLocations";
import InviteTeam from "./pages/onboarding/InviteTeam";
import WelcomeExistingUser from "./pages/onboarding/WelcomeExistingUser";
import ProfileSetup from "./pages/onboarding/ProfileSetup";

// Route structure with protected routes
function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <OnboardingProvider>
          <BrowserRouter>
            <Routes>
              <Route path="/auth" element={<Auth />} />
              
              {/* Onboarding Routes */}
              <Route path="/onboarding/welcome-new" element={<ProtectedRoute><WelcomeNewUser /></ProtectedRoute>} />
              <Route path="/onboarding/create-company" element={<ProtectedRoute><CreateCompany /></ProtectedRoute>} />
              <Route path="/onboarding/add-locations" element={<ProtectedRoute><AddLocations /></ProtectedRoute>} />
              <Route path="/onboarding/invite-team" element={<ProtectedRoute><InviteTeam /></ProtectedRoute>} />
              <Route path="/onboarding/welcome-existing" element={<ProtectedRoute><WelcomeExistingUser /></ProtectedRoute>} />
              <Route path="/onboarding/profile-setup" element={<ProtectedRoute><ProfileSetup /></ProtectedRoute>} />
              
              {/* Main App Routes */}
              <Route path="/" element={<ProtectedRoute><Index /></ProtectedRoute>} />
              <Route path="/master-data" element={<ProtectedRoute><MasterDataMapping /></ProtectedRoute>} />
              <Route path="/integrations" element={<ProtectedRoute><Integrations /></ProtectedRoute>} />
              <Route path="/alerts" element={<ProtectedRoute><Alerts /></ProtectedRoute>} />
              <Route path="/settings" element={<ProtectedRoute><Settings /></ProtectedRoute>} />
              <Route path="/profile" element={<ProtectedRoute><UserProfile /></ProtectedRoute>} />
              
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </OnboardingProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}
```

### Authentication System

```typescript
// hooks/useAuth.tsx - Authentication hook with Supabase
import { useState, useEffect, createContext, useContext, ReactNode } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signOut: () => Promise<void>;
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);
      }
    );

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signOut = async () => {
    await supabase.auth.signOut();
  };

  return <AuthContext.Provider value={{ user, session, loading, signOut }}>{children}</AuthContext.Provider>;
}
```

## Core Pages and Components

### 1. Dashboard (Index.tsx)
- Executive dashboard with business metrics
- Location selector for multi-location view
- Financial performance charts
- Operational efficiency tables
- Business alerts panel
- Onboarding welcome card for new users
- Guided walkthrough system

### 2. Master Data Mapping System
- 15 data type tabs: Locations, Menu Items, Menu Categories, Employees, etc.
- Smart mapping with confidence scores
- Bulk operations for mappings
- Data quality metrics
- Unmapped items management

### 3. Integration Wizard (5-step process)
- Step 1: Integration type selection (19 integration types)
- Step 2: Configuration with credentials
- Step 3: Connection testing with data preview
- Step 4: Data mapping and sync frequency
- Step 5: Review and activation

### 4. Alerts System
- Real-time monitoring dashboard
- Filtering by severity, status, location
- Bulk actions for alert management
- Alert generation from data issues
- Kitchen-themed branding

### 5. User Management
- Profile management
- Team member invitations
- Role-based access control
- Company settings

## Database Schema (Supabase)

### Core Tables

```sql
-- Companies
CREATE TABLE public.companies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  industry TEXT,
  created_by UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- User Profiles
CREATE TABLE public.profiles (
  id UUID PRIMARY KEY,
  full_name TEXT,
  email TEXT,
  role TEXT DEFAULT 'user',
  company_id UUID REFERENCES companies(id),
  has_completed_onboarding BOOLEAN DEFAULT false,
  onboarding_step INTEGER DEFAULT 1,
  sms_alerts BOOLEAN DEFAULT true,
  email_alerts BOOLEAN DEFAULT true,
  phone TEXT,
  timezone TEXT DEFAULT 'America/New_York',
  invited_by UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Master Data Tables (15 tables)
CREATE TABLE public.master_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  address TEXT,
  city TEXT,
  state TEXT,
  region TEXT,
  location_type TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

CREATE TABLE public.master_menu_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  category TEXT,
  price_range TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Data Mapping System
CREATE TYPE public.mapping_status AS ENUM ('pending', 'approved', 'rejected');
CREATE TYPE public.confidence_level AS ENUM ('high', 'medium', 'low');
CREATE TYPE public.data_type AS ENUM (
  'location', 'menu_item', 'menu_category', 'employee', 'day_part',
  'ingredient_supply', 'restaurant_operator', 'survey_question',
  'food_sales', 'menu_product', 'date'
);

CREATE TABLE public.data_mappings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  source_value TEXT NOT NULL,
  master_table TEXT NOT NULL,
  master_id UUID NOT NULL,
  data_type data_type NOT NULL,
  confidence_score confidence_level DEFAULT 'medium',
  status mapping_status DEFAULT 'pending',
  source_system_id UUID,
  mapped_by UUID,
  approved_by UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Integration Management
CREATE TABLE public.integration_status (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  integration_name TEXT NOT NULL,
  integration_type TEXT NOT NULL,
  status TEXT DEFAULT 'inactive',
  config JSONB,
  location_id UUID,
  last_sync_at TIMESTAMP WITH TIME ZONE,
  next_sync_at TIMESTAMP WITH TIME ZONE,
  sync_frequency TEXT DEFAULT 'hourly',
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Data Ingestion Logs
CREATE TABLE public.data_ingestion_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  source_type TEXT NOT NULL,
  source_name TEXT NOT NULL,
  location_id UUID,
  status TEXT DEFAULT 'processing',
  records_processed INTEGER DEFAULT 0,
  records_failed INTEGER DEFAULT 0,
  error_details JSONB,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  completed_at TIMESTAMP WITH TIME ZONE,
  created_by UUID
);

-- Team Management
CREATE TABLE public.team_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT NOT NULL,
  role TEXT NOT NULL,
  company_id UUID NOT NULL REFERENCES companies(id),
  invitation_token TEXT NOT NULL DEFAULT encode(gen_random_bytes(32), 'hex'),
  status TEXT DEFAULT 'pending',
  invited_by UUID,
  accepted_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (now() + interval '7 days'),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

### Sample Data for Testing

```json
// Sample Companies
{
  "id": "123e4567-e89b-12d3-a456-************",
  "name": "Pizza Palace Inc",
  "industry": "Quick Service Restaurant",
  "created_at": "2024-01-15T10:00:00Z"
}

// Sample Profiles
{
  "id": "user123",
  "full_name": "John Manager",
  "role": "Business Admin",
  "company_id": "123e4567-e89b-12d3-a456-************",
  "has_completed_onboarding": true,
  "onboarding_step": 6
}

// Sample Integration Status
{
  "integration_name": "Toast POS - Downtown",
  "integration_type": "toast",
  "status": "active",
  "last_sync_at": "2024-01-15T09:30:00Z",
  "records_processed": 1250,
  "sync_frequency": "real-time"
}

// Sample Data Ingestion Logs (for alert generation)
{
  "source_type": "pos",
  "source_name": "Toast Downtown",
  "status": "failed",
  "records_processed": 0,
  "records_failed": 500,
  "error_details": {"error": "API rate limit exceeded"}
}
```

## Key Business Logic

### 1. Master Data Mapping Logic

```typescript
// Master data types with mapping rules
export const MASTER_DATA_TYPES = [
  {
    id: 'location',
    name: 'Locations',
    table: 'master_locations',
    fields: ['name', 'address', 'city', 'state', 'region']
  },
  {
    id: 'menu_item',
    name: 'Menu Items', 
    table: 'master_menu_items',
    fields: ['name', 'description', 'category', 'price_range']
  },
  // ... 13 more data types
];

// Mapping confidence calculation
export const calculateMappingConfidence = (sourceValue: string, masterValue: string): 'high' | 'medium' | 'low' => {
  const similarity = calculateStringSimilarity(sourceValue, masterValue);
  if (similarity > 0.9) return 'high';
  if (similarity > 0.7) return 'medium';
  return 'low';
};
```

### 2. Alert Generation System

```typescript
// Alert generation from data issues
export const generateAlertsFromData = (ingestionLogs: any[], integrationStatus: any[]) => {
  const alerts = [];
  
  // Failed ingestion alerts
  ingestionLogs.forEach(log => {
    if (log.status === 'failed') {
      alerts.push({
        id: generateId(),
        title: `Data Ingestion Failed: ${log.source_name}`,
        severity: 'critical',
        status: 'active',
        category: 'Data Quality',
        created_at: log.started_at
      });
    }
  });
  
  // Integration health alerts
  integrationStatus.forEach(integration => {
    if (integration.status === 'error') {
      alerts.push({
        id: generateId(),
        title: `Integration Error: ${integration.integration_name}`,
        severity: 'high',
        status: 'active',
        category: 'Sync Health'
      });
    }
  });
  
  return alerts;
};
```

### 3. Integration Wizard Logic

```typescript
// 19 supported integration types
export const INTEGRATION_TYPES = [
  {
    id: "toast",
    name: "Toast POS",
    category: "POS Systems",
    description: "Connect your Toast point-of-sale system for real-time sales data",
    features: ["Sales Data", "Menu Items", "Customer Data"],
    configFields: [
      { name: "apiKey", label: "API Key", type: "password", required: true },
      { name: "locationId", label: "Location ID", type: "text", required: true },
      { name: "environment", label: "Environment", type: "select", options: ["production", "sandbox"] }
    ]
  },
  // ... 18 more integration types including Square, Clover, QuickBooks, Google Analytics, etc.
];

// Integration testing logic
export const testIntegrationConnection = async (type: string, config: any) => {
  // Simulate API testing with random success/failure
  const isSuccess = Math.random() > 0.3; // 70% success rate
  
  return {
    success: isSuccess,
    responseTime: Math.floor(Math.random() * 500) + 100,
    dataPreview: isSuccess ? {
      recordsFound: Math.floor(Math.random() * 1000) + 100,
      sampleData: generateSampleData(type)
    } : null,
    error: !isSuccess ? "Invalid API credentials" : null
  };
};
```

### 4. Walkthrough System

```typescript
// Page-specific walkthrough steps
export const alertsWalkthroughSteps = [
  {
    target: '.alerts-header',
    title: 'Welcome to Kitchen Alerts',
    content: 'Monitor and manage all your data kitchen alerts from this central command center.',
    position: 'bottom'
  },
  {
    target: '.alerts-filters',
    title: 'Filter Your Ingredients',
    content: 'Use these filters to find specific types of alerts by severity, status, or location.',
    position: 'right'
  },
  // ... 6 more steps
];

// Walkthrough state management
export const useWalkthrough = () => {
  const [showWalkthrough, setShowWalkthrough] = useState(false);
  const [hasSeenWalkthrough, setHasSeenWalkthrough] = useState(false);

  useEffect(() => {
    const completed = localStorage.getItem('walkthrough-completed');
    if (!completed) {
      setTimeout(() => setShowWalkthrough(true), 2000);
    } else {
      setHasSeenWalkthrough(true);
    }
  }, []);

  const completeWalkthrough = () => {
    localStorage.setItem('walkthrough-completed', 'true');
    setShowWalkthrough(false);
    setHasSeenWalkthrough(true);
  };

  return { showWalkthrough, hasSeenWalkthrough, completeWalkthrough };
};
```

## Critical Testing Areas

### 1. Authentication Flow
- **Test Cases**: Login/logout, session persistence, protected routes
- **Edge Cases**: Expired sessions, invalid credentials, network issues
- **Expected Behavior**: Secure access, proper redirects, error handling

### 2. Onboarding Process (6 steps)
- **Step 1**: Welcome new users
- **Step 2**: Company creation
- **Step 3**: Location setup
- **Step 4**: Team invitations
- **Step 5**: Existing user welcome
- **Step 6**: Profile completion
- **Critical**: State persistence, validation, navigation

### 3. Master Data Mapping
- **15 Data Types**: Each with specific validation rules
- **Mapping Logic**: Confidence scoring, bulk operations
- **Data Quality**: Metrics calculation, unmapped items
- **Performance**: Large dataset handling

### 4. Integration Wizard
- **19 Integration Types**: Each with unique configuration
- **5-Step Process**: Type selection → Configuration → Testing → Data mapping → Activation
- **Connection Testing**: API validation, error handling
- **Data Preview**: Sample data display, field mapping

### 5. Alerts System
- **Alert Generation**: From failed ingestions and integration errors
- **Filtering**: Multi-dimensional (severity, status, location)
- **Bulk Actions**: Mark read, bulk operations
- **Real-time Updates**: Data refresh, state management

### 6. User Interface
- **Responsive Design**: Mobile, tablet, desktop
- **Accessibility**: Keyboard navigation, screen readers
- **Performance**: Loading states, smooth animations
- **Walkthrough System**: 8 pages with guided tours

## Known Issues and Limitations

### Database Limitations
1. No dedicated alerts table - alerts generated from existing data
2. Limited RLS policies - some tables missing user-based access control
3. No audit trail for data changes
4. Missing indexes for performance optimization

### Feature Limitations
1. **Real-time Updates**: Manual refresh required for new alerts
2. **Offline Support**: No offline functionality
3. **File Upload**: Limited file type support
4. **Bulk Operations**: No progress indicators for large operations
5. **Data Export**: Limited export formats

### Integration Constraints
1. **API Rate Limits**: No rate limiting implementation
2. **Error Recovery**: Basic retry logic only
3. **Data Validation**: Limited field validation
4. **Webhook Support**: Not implemented

### UI/UX Issues
1. **Loading States**: Inconsistent across components
2. **Error Messages**: Generic error handling
3. **Mobile Experience**: Some components not fully optimized
4. **Accessibility**: Missing ARIA labels in some areas

## Testing Priorities

### High Priority (P0)
1. **Authentication Security**: Login/logout, session management
2. **Data Integrity**: Master data mapping accuracy
3. **Critical User Flows**: Onboarding completion, integration setup
4. **Database Operations**: CRUD operations, data consistency

### Medium Priority (P1)
1. **UI Responsiveness**: Cross-device compatibility
2. **Performance**: Page load times, large dataset handling
3. **Integration Testing**: External API connections
4. **Error Handling**: Graceful degradation

### Low Priority (P2)
1. **Visual Polish**: Styling consistency, animations
2. **Edge Cases**: Unusual user behaviors
3. **Enhancement Features**: Nice-to-have functionality

## Setup Instructions for Testing

### Prerequisites
1. **Supabase Project**: Connected with full schema
2. **Test Data**: Sample companies, users, mappings
3. **Environment**: Development mode with console access
4. **Browser**: Chrome/Firefox with DevTools

### Test Data Setup
```sql
-- Insert test company
INSERT INTO companies (name, industry) VALUES ('Test Restaurant Corp', 'Quick Service');

-- Insert test user
INSERT INTO profiles (id, full_name, role, has_completed_onboarding) 
VALUES ('test-user-id', 'Test User', 'Business Admin', true);

-- Insert sample integration data
INSERT INTO integration_status (integration_name, integration_type, status, last_sync_at)
VALUES ('Toast Downtown', 'toast', 'active', now() - interval '2 hours');

-- Insert failed ingestion for alert testing
INSERT INTO data_ingestion_logs (source_name, source_type, status, records_failed)
VALUES ('Toast API', 'pos', 'failed', 100);
```

### Testing Environment Variables
- **Supabase URL**: https://sabgwpockxkkbacwzyaa.supabase.co
- **Supabase Anon Key**: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
- **Project ID**: sabgwpockxkkbacwzyaa

### Test User Accounts
- **Admin User**: <EMAIL> / password123
- **Regular User**: <EMAIL> / password123
- **New User**: (create during onboarding testing)

## Expected Testing Outcomes

### Success Criteria
1. **All Core Flows Work**: Authentication, onboarding, data management
2. **No Critical Errors**: No application crashes or data loss
3. **Performance Standards**: Page loads < 3 seconds, smooth interactions
4. **Data Accuracy**: Correct mapping calculations, alert generation
5. **Security**: Proper access control, data protection

### Failure Indicators
1. **Authentication Issues**: Login failures, session problems
2. **Data Corruption**: Incorrect mappings, missing records
3. **UI Breaks**: Layout issues, non-functional components
4. **Performance Problems**: Slow loading, memory leaks
5. **Security Vulnerabilities**: Unauthorized access, data exposure

## Bug Reporting Format

```
**Priority**: [P0/P1/P2]
**Component**: [Authentication/Dashboard/MasterData/etc.]
**Test Case**: [Specific test scenario]
**Environment**: [Browser, device, screen size]

**Description**: Brief issue summary

**Steps to Reproduce**:
1. Step one
2. Step two
3. Step three

**Expected Result**: What should happen
**Actual Result**: What actually happens
**Screenshots**: [If applicable]
**Console Errors**: [JavaScript errors]
**Database State**: [Relevant data state]
```

---

**This package provides everything needed for comprehensive QA testing of the CONVX Data Kitchen platform. The focus should be on user experience, data integrity, and system reliability across all core workflows.**
